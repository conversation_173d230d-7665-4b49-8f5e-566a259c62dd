import { describe, it, expect } from "vitest";
import { convexTest } from "convex-test";
import schema from "../convex/schema";

describe("Schema validation", () => {
  it("should define required tables from story requirements", () => {
    const expectedTables = ["users", "projects", "projectMessages"];
    
    expectedTables.forEach(tableName => {
      expect(schema.tables).toHaveProperty(tableName);
    });
  });

  it("should initialize convex test environment successfully", async () => {
    const t = convexTest(schema);
    
    // Test that we can initialize the test environment
    expect(t).toBeDefined();
  });

  it("should have proper table structure", () => {
    // Simple validation that our tables exist and have the expected structure
    expect(schema.tables.users).toBeDefined();
    expect(schema.tables.projects).toBeDefined();
    expect(schema.tables.projectMessages).toBeDefined();
    
    // Additional comprehensive tables should also exist
    expect(schema.tables.conversations).toBeDefined();
    expect(schema.tables.messages).toBeDefined();
  });
});