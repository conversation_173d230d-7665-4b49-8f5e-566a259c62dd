import { FlatCompat } from "@eslint/eslintrc";
import { dirname } from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
	baseDirectory: __dirname,
});

const eslintConfig = [
	...compat.extends("next/core-web-vitals", "next/typescript"),
	// Global TypeScript rules
	{
		files: ["**/*.ts", "**/*.tsx"],
		ignores: ["docs/**/*", "./convex/_generated/"],
		rules: {
			"@typescript-eslint/no-explicit-any": "error",
			"@typescript-eslint/no-unused-vars": "error",
			"@typescript-eslint/no-inferrable-types": "error",
		},
	},
	// Override rules for test files
	{
		files: [
			"**/__tests__/**/*",
			"**/*.test.*",
			"**/*.spec.*",
			"**/jest.setup.js",
		],
		rules: {
			"@typescript-eslint/no-explicit-any": "off",
		},
	},
];

export default eslintConfig;
