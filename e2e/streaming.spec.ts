import { test, expect } from '@playwright/test';

test.describe('Streaming Chat', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/chat');
  });

  test('should display streaming indicator when sending a message', async ({ page }) => {
    // Type a test message
    const messageInput = page.getByTestId('message-input');
    await messageInput.fill('Hello, this is a test message');

    // Submit the message
    const submitButton = page.getByTestId('submit-button');
    await submitButton.click();

    // Check that streaming indicator appears
    const streamingIndicator = page.locator('[data-testid="streaming-indicator"]');
    await expect(streamingIndicator).toBeVisible({ timeout: 5000 });

    // Wait for streaming to complete (streaming indicator should disappear)
    await expect(streamingIndicator).not.toBeVisible({ timeout: 30000 });

    // Verify the user message appears
    const userMessage = page.getByText('Hello, this is a test message');
    await expect(userMessage).toBeVisible();

    // Verify an assistant response appears
    const assistantMessages = page.locator('[data-testid="assistant-message"]');
    await expect(assistantMessages).toHaveCount(1, { timeout: 5000 });
  });

  test('should handle streaming cancellation', async ({ page }) => {
    // Type a test message
    const messageInput = page.getByTestId('message-input');
    await messageInput.fill('Write a very long story about space exploration');

    // Submit the message
    const submitButton = page.getByTestId('submit-button');
    await submitButton.click();

    // Wait for streaming to start
    const streamingIndicator = page.locator('[data-testid="streaming-indicator"]');
    await expect(streamingIndicator).toBeVisible({ timeout: 5000 });

    // Click cancel button if it appears
    const cancelButton = page.getByText('Stop');
    if (await cancelButton.isVisible()) {
      await cancelButton.click();
      
      // Verify streaming stops
      await expect(streamingIndicator).not.toBeVisible({ timeout: 5000 });
    }
  });

  test('should display error message on streaming failure', async ({ page }) => {
    // This test would require mocking the API to return an error
    // For now, we'll just check that error handling UI exists
    const errorDisplay = page.locator('[data-testid="error-display"]');
    
    // The error display should not be visible initially
    await expect(errorDisplay).not.toBeVisible();
  });
});