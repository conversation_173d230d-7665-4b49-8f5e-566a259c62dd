import { test, expect } from '@playwright/test';

test.describe('Convex Agent - Basic Message Flow', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/chat');
  });

  test('sends message and receives response', async ({ page }) => {
    const messageInput = page.getByTestId('message-input');
    const submitButton = page.getByTestId('submit-button');
    
    // Send a message
    await messageInput.fill('Test message');
    
    // Wait for button to be enabled and click
    await expect(submitButton).toBeEnabled();
    await submitButton.click();

    // Wait for streaming to start and complete
    await expect(page.locator('[data-testid="streaming-indicator"]')).toBeVisible({ timeout: 5000 });
    await expect(page.locator('[data-testid="streaming-indicator"]')).not.toBeVisible({ timeout: 30000 });

    // Verify user message appears
    await expect(page.locator('[data-testid="user-message"]')).toBeVisible();
    
    // Verify assistant message appears
    await expect(page.locator('[data-testid="assistant-message"]')).toBeVisible();
  });

  test('handles multiple message exchanges', async ({ page }) => {
    const messageInput = page.getByTestId('message-input');
    const submitButton = page.getByTestId('submit-button');

    // Send first message
    await messageInput.fill('First message');
    await expect(submitButton).toBeEnabled();
    await submitButton.click();
    
    await expect(page.locator('[data-testid="streaming-indicator"]')).toBeVisible({ timeout: 5000 });
    await expect(page.locator('[data-testid="streaming-indicator"]')).not.toBeVisible({ timeout: 30000 });

    // Send second message
    await messageInput.fill('Second message');
    await expect(submitButton).toBeEnabled();
    await submitButton.click();

    await expect(page.locator('[data-testid="streaming-indicator"]')).toBeVisible({ timeout: 5000 });
    await expect(page.locator('[data-testid="streaming-indicator"]')).not.toBeVisible({ timeout: 30000 });

    // Should have 2 user messages
    await expect(page.locator('[data-testid="user-message"]')).toHaveCount(2);
    
    // Should have 2 assistant messages
    await expect(page.locator('[data-testid="assistant-message"]')).toHaveCount(2);
  });

  // TODO: Fix persistence test - see issue #13
  // test('persists messages after page reload', async ({ page }) => {
  //   const messageInput = page.getByTestId('message-input');
  //   const submitButton = page.getByTestId('submit-button');

  //   // Send message
  //   await messageInput.fill('Persistence test');
  //   await expect(submitButton).toBeEnabled();
  //   await submitButton.click();

  //   await expect(page.locator('[data-testid="streaming-indicator"]')).toBeVisible({ timeout: 5000 });
  //   await expect(page.locator('[data-testid="streaming-indicator"]')).not.toBeVisible({ timeout: 30000 });

  //   // Reload page
  //   await page.reload();

  //   // Messages should still be there
  //   await expect(page.locator('[data-testid="user-message"]')).toHaveCount(1); // user
  //   await expect(page.locator('[data-testid="assistant-message"]')).toHaveCount(1); // assistant
  // });

  test('streaming controls work correctly', async ({ page }) => {
    const messageInput = page.getByTestId('message-input');
    const submitButton = page.getByTestId('submit-button');
    
    await messageInput.fill('Long response test');
    await expect(submitButton).toBeEnabled();
    await submitButton.click();

    // Wait for streaming to start
    await expect(page.locator('[data-testid="streaming-indicator"]')).toBeVisible({ timeout: 5000 });
    
    // Input should be disabled during streaming
    await expect(messageInput).toBeDisabled();
    
    // Stop button should be visible (use specific aria-label to avoid confusion)
    await expect(page.getByRole('button', { name: 'Stop generation' })).toBeVisible();
    
    // Wait for completion
    await expect(page.locator('[data-testid="streaming-indicator"]')).not.toBeVisible({ timeout: 30000 });
    
    // Input should be enabled again
    await expect(messageInput).not.toBeDisabled();
  });

  test('no console errors during message flow', async ({ page }) => {
    const consoleErrors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });

    const messageInput = page.getByTestId('message-input');
    const submitButton = page.getByTestId('submit-button');

    await messageInput.fill('Error test message');
    await expect(submitButton).toBeEnabled();
    await submitButton.click();

    await expect(page.locator('[data-testid="streaming-indicator"]')).toBeVisible({ timeout: 5000 });
    await expect(page.locator('[data-testid="streaming-indicator"]')).not.toBeVisible({ timeout: 30000 });

    // Should have no console errors (filter out React DevTools message)
    expect(consoleErrors.filter(error => !error.includes('Download the React DevTools'))).toHaveLength(0);
  });
});