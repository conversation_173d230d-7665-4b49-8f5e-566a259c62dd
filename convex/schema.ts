import { authTables } from "@convex-dev/auth/server";
import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

/**
 * Convex schema for PRDGeneral application
 *
 * Defines data models for:
 * - User authentication and sessions
 * - Chat conversations and messages
 * - PRD generation and storage
 * - Usage tracking and rate limiting
 */
export default defineSchema({
	// Convex Auth tables
	...authTables,
	// User authentication and profiles
	users: defineTable({
		// Basic user info
		email: v.optional(v.string()),
		name: v.optional(v.string()),

		// Authentication provider info
		tokenIdentifier: v.string(), // From Convex auth

		// User preferences
		preferredAIProvider: v.optional(
			v.union(v.literal("anthropic"), v.literal("openai")),
		),

		// Usage tracking
		totalPRDsGenerated: v.number(),
		planType: v.union(v.literal("free"), v.literal("pro")),

		// Timestamps
		createdAt: v.number(),
		lastActiveAt: v.number(),

		// Soft delete
		isDeleted: v.optional(v.boolean()),
	})
		.index("by_token", ["tokenIdentifier"])
		.index("email", ["email"])
		.searchIndex("search_users", {
			searchField: "name",
			filterFields: ["isDeleted"],
		}),

	// Projects for PRD generation and management
	projects: defineTable({
		// Ownership
		userId: v.id("users"),

		// Project details
		name: v.string(),
		prdContent: v.optional(v.string()),

		// Timestamps
		createdAt: v.number(),
		updatedAt: v.number(),
	}).index("by_user", ["userId", "createdAt"]),

	// Simple project messages as specified in story requirements
	projectMessages: defineTable({
		// Relationship
		projectId: v.id("projects"),

		// Message content
		author: v.union(v.literal("user"), v.literal("TheGeneral")),
		text: v.string(),

		// Timestamps
		createdAt: v.number(),
	}).index("by_project", ["projectId", "createdAt"]),

	// Agent threads for chat conversations
	threads: defineTable({
		// Ownership
		userId: v.string(), // User ID or "anonymous"

		// Thread metadata
		title: v.string(),

		// Timestamps
		createdAt: v.number(),
		updatedAt: v.number(),
	}).index("by_user", ["userId", "createdAt"]),

	// Chat conversations/sessions (legacy)
	conversations: defineTable({
		// Ownership
		userId: v.optional(v.id("users")), // Optional for anonymous users
		sessionId: v.string(), // For anonymous users

		// Conversation metadata
		title: v.optional(v.string()), // Auto-generated from first message
		status: v.union(
			v.literal("active"),
			v.literal("completed"),
			v.literal("abandoned"),
		),

		// Mode tracking
		currentMode: v.union(
			v.literal("clarification"),
			v.literal("generation"),
			v.literal("refinement"),
		),

		// Progress tracking
		clarificationComplete: v.boolean(),
		prdGenerated: v.boolean(),

		// Cascade flow state
		gateStates: v.optional(
			v.record(
				v.string(),
				v.union(v.literal("PASS"), v.literal("FAIL"), v.literal("PENDING")),
			),
		),
		cascadeVersion: v.optional(v.number()),

		// Timestamps
		createdAt: v.number(),
		updatedAt: v.number(),
		lastMessageAt: v.number(),

		// Rate limiting context
		ipAddress: v.optional(v.string()),
		userAgent: v.optional(v.string()),
	})
		.index("by_user", ["userId"])
		.index("by_session", ["sessionId"])
		.index("by_ip_date", ["ipAddress", "createdAt"])
		.index("by_status", ["status", "updatedAt"]),

	// PRD Sessions for agent-guided PRD creation
	prdSessions: defineTable({
		userId: v.string(),
		projectName: v.string(),
		targetAudience: v.optional(v.string()),
		status: v.union(
			v.literal("active"),
			v.literal("paused"),
			v.literal("completed"),
			v.literal("abandoned"),
		),
		currentStep: v.string(),
		questionnaire: v.record(
			v.string(),
			v.union(v.string(), v.number(), v.boolean(), v.array(v.string())),
		),
		generatedPRD: v.optional(
			v.object({
				content: v.string(),
				format: v.union(v.literal("markdown"), v.literal("json")),
				generatedAt: v.number(),
				version: v.string(),
			}),
		),
		progress: v.object({
			completedSteps: v.array(v.string()),
			totalSteps: v.number(),
			percentComplete: v.number(),
		}),
		createdAt: v.number(),
		updatedAt: v.number(),
	})
		.index("by_user", ["userId"])
		.index("by_status", ["status"]),

	// AI Agents for PRD creation assistance
	agents: defineTable({
		sessionId: v.id("prdSessions"),
		type: v.string(), // "TheGeneral"
		personality: v.string(), // "drill_instructor"
		status: v.union(
			v.literal("active"),
			v.literal("paused"),
			v.literal("completed"),
			v.literal("error"),
		),
		currentWorkflow: v.string(), // "prd_questionnaire"
		context: v.record(
			v.string(),
			v.union(v.string(), v.number(), v.boolean(), v.object({})),
		),
		memory: v.optional(
			v.object({
				userPreferences: v.record(v.string(), v.union(v.string(), v.boolean())),
				conversationSummary: v.optional(v.string()),
				motivationLevel: v.string(),
			}),
		),
		metrics: v.optional(
			v.object({
				messagesGenerated: v.number(),
				avgResponseTime: v.number(),
				tokensUsed: v.number(),
			}),
		),
		createdAt: v.number(),
		lastActiveAt: v.number(),
	})
		.index("by_session", ["sessionId"])
		.index("by_status", ["status"]),

	// Enhanced Messages for agent interactions
	messages: defineTable({
		sessionId: v.id("prdSessions"),
		senderId: v.string(),
		senderType: v.union(
			v.literal("user"),
			v.literal("agent"),
			v.literal("system"),
		),
		content: v.string(),
		messageType: v.union(
			v.literal("greeting"),
			v.literal("question"),
			v.literal("response"),
			v.literal("validation"),
			v.literal("completion"),
		),
		metadata: v.optional(
			v.object({
				agentPersonality: v.optional(v.string()),
				workflowStep: v.optional(v.string()),
				aiProvider: v.optional(v.string()),
				confidence: v.optional(v.number()),
			}),
		),
		timestamp: v.number(),
		sequenceNumber: v.number(),
	})
		.index("by_session", ["sessionId"])
		.index("by_session_timestamp", ["sessionId", "timestamp"])
		.index("by_session_sequence", ["sessionId", "sequenceNumber"]),

	// Legacy individual chat messages (keeping for backwards compatibility)
	legacyMessages: defineTable({
		// Relationship
		conversationId: v.id("conversations"),

		// Message content
		role: v.union(
			v.literal("user"),
			v.literal("assistant"),
			v.literal("system"),
		),
		content: v.string(),

		// Metadata
		messageIndex: v.number(), // Order in conversation
		aiProvider: v.optional(
			v.union(v.literal("anthropic"), v.literal("openai")),
		),
		model: v.optional(v.string()),

		// Token usage (for cost tracking)
		promptTokens: v.optional(v.number()),
		completionTokens: v.optional(v.number()),
		totalTokens: v.optional(v.number()),

		// Generation metadata
		temperature: v.optional(v.number()),
		maxTokens: v.optional(v.number()),
		generationTime: v.optional(v.number()), // milliseconds

		// Timestamps
		createdAt: v.number(),

		// Error tracking
		hasError: v.optional(v.boolean()),
		errorMessage: v.optional(v.string()),
	})
		.index("by_conversation", ["conversationId", "messageIndex"])
		.index("by_role", ["role", "createdAt"])
		.index("by_error", ["hasError", "createdAt"]),

	// Generated PRDs
	prds: defineTable({
		// Ownership
		conversationId: v.id("conversations"),
		userId: v.optional(v.id("users")),

		// PRD content
		title: v.string(),
		content: v.string(), // Markdown content

		// Generation metadata
		generationType: v.union(v.literal("initial"), v.literal("refinement")),
		aiProvider: v.union(v.literal("anthropic"), v.literal("openai")),
		model: v.string(),

		// Token usage
		promptTokens: v.number(),
		completionTokens: v.number(),
		totalTokens: v.number(),

		// Quality metrics
		wordCount: v.number(),
		estimatedReadingTime: v.number(), // minutes

		// User actions
		downloaded: v.boolean(),
		downloadCount: v.number(),

		// Timestamps
		createdAt: v.number(),
		updatedAt: v.number(),

		// Version control
		version: v.number(),
		parentPRDId: v.optional(v.id("prds")), // For refinements
	})
		.index("by_conversation", ["conversationId", "version"])
		.index("by_user", ["userId", "createdAt"])
		.index("by_date", ["createdAt"])
		.searchIndex("search_prds", {
			searchField: "title",
			filterFields: ["userId"],
		}),

	// Rate limiting state for token bucket algorithm
	rateLimitState: defineTable({
		// Unique key for rate limiter + identifier combination
		key: v.string(),

		// Rate limiter metadata
		limiterName: v.string(),
		identifier: v.string(), // User ID, session ID, or IP address

		// Token bucket state
		tokens: v.number(),
		lastRefill: v.number(),

		// Timestamps
		createdAt: v.number(),
		updatedAt: v.number(),
	})
		.index("by_key", ["key"])
		.index("by_limiter", ["limiterName", "identifier"])
		.index("by_updated", ["updatedAt"]),

	// Rate limiting and usage tracking
	usageTracking: defineTable({
		// Identification
		userId: v.optional(v.id("users")),
		ipAddress: v.optional(v.string()),
		sessionId: v.optional(v.string()),

		// Action tracking
		action: v.union(
			v.literal("prd_generation"),
			v.literal("message_sent"),
			v.literal("api_call"),
		),

		// Rate limiting windows
		hourWindow: v.string(), // YYYY-MM-DD:HH format
		dayWindow: v.string(), // YYYY-MM-DD format
		monthWindow: v.string(), // YYYY-MM format

		// Counts
		count: v.number(),

		// Cost tracking
		tokenCost: v.optional(v.number()), // USD cents

		// Timestamps
		createdAt: v.number(),
		updatedAt: v.number(),
	})
		.index("by_user_day", ["userId", "dayWindow", "action"])
		.index("by_ip_day", ["ipAddress", "dayWindow", "action"])
		.index("by_session_day", ["sessionId", "dayWindow", "action"])
		.index("by_hour", ["hourWindow", "action"])
		.index("by_day", ["dayWindow", "action"]),

	// System configuration and feature flags
	systemConfig: defineTable({
		// Configuration key
		key: v.string(),

		// Configuration value (JSON string)
		value: v.string(),

		// Metadata
		description: v.optional(v.string()),
		category: v.string(), // "rate_limiting", "ai_config", "features", etc.

		// Timestamps
		createdAt: v.number(),
		updatedAt: v.number(),

		// Versioning
		version: v.number(),
	})
		.index("by_key", ["key"])
		.index("by_category", ["category", "key"]),

	// Legacy streaming sessions table removed - agent system handles streaming internally

	// Mode detection analytics and improvement
	modeDetectionLogs: defineTable({
		// Context
		conversationId: v.id("conversations"),

		// Input
		message: v.string(),

		// Detection results
		detectedMode: v.union(
			v.literal("clarification"),
			v.literal("generation"),
			v.literal("refinement"),
		),
		confidence: v.number(),

		// Keyword analysis results
		keywordAnalysis: v.object({
			prdGenerationScore: v.number(),
			clarificationCompleteScore: v.number(),
			detectedIntent: v.string(),
		}),

		// Timestamps
		timestamp: v.number(),
	})
		.index("by_conversation", ["conversationId", "timestamp"])
		.index("by_mode", ["detectedMode", "timestamp"])
		.index("by_confidence", ["confidence", "timestamp"]),

	// Mode transition tracking for analytics
	modeTransitionLogs: defineTable({
		// Context
		conversationId: v.id("conversations"),

		// Transition details
		fromMode: v.union(
			v.literal("clarification"),
			v.literal("generation"),
			v.literal("refinement"),
		),
		toMode: v.union(
			v.literal("clarification"),
			v.literal("generation"),
			v.literal("refinement"),
		),

		// Transition metadata
		reason: v.string(), // Why the transition occurred
		userTriggered: v.boolean(), // Was this user-initiated or automatic?

		// Timestamps
		timestamp: v.number(),
	})
		.index("by_conversation", ["conversationId", "timestamp"])
		.index("by_transition", ["fromMode", "toMode", "timestamp"])
		.index("by_trigger", ["userTriggered", "timestamp"]),

	// Error logging and monitoring
	errorLogs: defineTable({
		// Context
		userId: v.optional(v.id("users")),
		conversationId: v.optional(v.id("conversations")),

		// Error details
		errorType: v.string(),
		errorMessage: v.string(),
		stackTrace: v.optional(v.string()),

		// Request context
		route: v.optional(v.string()),
		method: v.optional(v.string()),
		userAgent: v.optional(v.string()),
		ipAddress: v.optional(v.string()),

		// Severity
		severity: v.union(
			v.literal("low"),
			v.literal("medium"),
			v.literal("high"),
			v.literal("critical"),
		),

		// Status
		resolved: v.boolean(),

		// Timestamps
		createdAt: v.number(),
		resolvedAt: v.optional(v.number()),
	})
		.index("by_user", ["userId", "createdAt"])
		.index("by_conversation", ["conversationId", "createdAt"])
		.index("by_severity", ["severity", "resolved", "createdAt"])
		.index("by_type", ["errorType", "createdAt"]),

	// Gate results for cascade flow tracking
	gate_results: defineTable({
		// Context
		conversationId: v.id("conversations"),

		// Gate information
		gate: v.union(
			v.literal("vagueness"),
			v.literal("focus"),
			v.literal("scope"),
			v.literal("specificity"),
		),

		// Result
		status: v.union(v.literal("pass"), v.literal("fail")),
		reasons: v.array(v.string()), // Always required - empty array for pass, populated for fail

		// Timestamps
		createdAt: v.number(),
	})
		.index("by_conversation", ["conversationId", "createdAt"])
		.index("by_gate", ["gate", "status", "createdAt"])
		.index("by_status", ["status", "createdAt"]),

	// Refinement sessions for specificity gate iteration tracking
	refinement_sessions: defineTable({
		// Context - keyed by conversationId (chatId)
		conversationId: v.id("conversations"),

		// Session state
		iteration: v.number(), // Current iteration count (1-3)
		lastQuestion: v.optional(v.string()), // Last clarification question asked

		// Timestamps
		createdAt: v.number(),
		updatedAt: v.number(),
	})
		.index("by_conversation", ["conversationId"])
		.index("by_updated", ["updatedAt"]),

	// Decision logs for clarification agent
	decision_logs: defineTable({
		// Context
		threadId: v.string(), // Agent thread ID
		featureId: v.optional(v.string()), // Feature being clarified
		userId: v.string(), // User ID or "anonymous"

		// Decision details
		originalFunction: v.string(), // Original function description
		decision: v.union(
			v.literal("removed"),
			v.literal("deferred"),
			v.literal("separate_ticket"),
		),
		reason: v.string(), // Why this decision was made

		// Timestamps
		timestamp: v.number(),
		createdAt: v.number(),
	})
		.index("by_thread", ["threadId", "timestamp"])
		.index("by_feature", ["featureId", "timestamp"])
		.index("by_user", ["userId", "timestamp"])
		.index("by_decision", ["decision", "timestamp"]),
});
