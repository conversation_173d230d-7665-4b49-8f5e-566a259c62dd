/**
 * Cascade State Management Utilities
 *
 * Provides consistent read/write access to gate states in the conversation table.
 * Used by the mainCascadeFlow mutation and other parts of the system that need
 * to track or query the current state of the gate validation process.
 */

import type { Id } from "../_generated/dataModel";
import type { MutationCtx, QueryCtx } from "../_generated/server";

/**
 * Gate state values that can be stored in the conversation
 */
export type GateState = "PASS" | "FAIL" | "PENDING";

/**
 * Gate names in the cascade flow
 */
export type GateName = "vagueness" | "focus" | "scope" | "specificity";

/**
 * Gate states record type for the conversation table
 */
export type GateStates = Record<string, GateState>;

/**
 * Set the state of a specific gate for a conversation
 *
 * @param ctx - Convex mutation context
 * @param conversationId - ID of the conversation to update
 * @param gate - Name of the gate
 * @param state - New state value for the gate
 */
export async function setGateState(
	ctx: MutationCtx,
	conversationId: Id<"conversations">,
	gate: GateName,
	state: GateState,
): Promise<void> {
	const conversation = await ctx.db.get(conversationId);
	if (!conversation) {
		throw new Error(`Conversation ${conversationId} not found`);
	}

	// Get current gate states or initialize empty object
	const currentGateStates = conversation.gateStates || {};

	// Update the specific gate state
	const updatedGateStates = {
		...currentGateStates,
		[gate]: state,
	};

	// Update conversation with new gate states and cascade version
	await ctx.db.patch(conversationId, {
		gateStates: updatedGateStates,
		cascadeVersion: conversation.cascadeVersion || 1,
		updatedAt: Date.now(),
	});
}

/**
 * Set multiple gate states at once for a conversation
 *
 * @param ctx - Convex mutation context
 * @param conversationId - ID of the conversation to update
 * @param states - Object mapping gate names to their new states
 */
export async function setGateStates(
	ctx: MutationCtx,
	conversationId: Id<"conversations">,
	states: Partial<Record<GateName, GateState>>,
): Promise<void> {
	const conversation = await ctx.db.get(conversationId);
	if (!conversation) {
		throw new Error(`Conversation ${conversationId} not found`);
	}

	// Get current gate states or initialize empty object
	const currentGateStates = conversation.gateStates || {};

	// Merge in the new states
	const updatedGateStates = {
		...currentGateStates,
		...states,
	};

	// Update conversation with new gate states and cascade version
	await ctx.db.patch(conversationId, {
		gateStates: updatedGateStates,
		cascadeVersion: conversation.cascadeVersion || 1,
		updatedAt: Date.now(),
	});
}

/**
 * Get all gate states for a conversation
 *
 * @param ctx - Convex query or mutation context
 * @param conversationId - ID of the conversation
 * @returns Gate states object or empty object if none set
 */
export async function getGateStates(
	ctx: QueryCtx | MutationCtx,
	conversationId: Id<"conversations">,
): Promise<GateStates> {
	const conversation = await ctx.db.get(conversationId);
	if (!conversation) {
		throw new Error(`Conversation ${conversationId} not found`);
	}

	return conversation.gateStates || {};
}

/**
 * Get the state of a specific gate for a conversation
 *
 * @param ctx - Convex query or mutation context
 * @param conversationId - ID of the conversation
 * @param gate - Name of the gate to check
 * @returns Gate state or undefined if not set
 */
export async function getGateState(
	ctx: QueryCtx | MutationCtx,
	conversationId: Id<"conversations">,
	gate: GateName,
): Promise<GateState | undefined> {
	const gateStates = await getGateStates(ctx, conversationId);
	return gateStates[gate];
}

/**
 * Check if all gates have passed for a conversation
 *
 * @param ctx - Convex query or mutation context
 * @param conversationId - ID of the conversation
 * @returns True if all gates have PASS state, false otherwise
 */
export async function allGatesPassed(
	ctx: QueryCtx | MutationCtx,
	conversationId: Id<"conversations">,
): Promise<boolean> {
	const gateStates = await getGateStates(ctx, conversationId);
	const requiredGates: GateName[] = [
		"vagueness",
		"focus",
		"scope",
		"specificity",
	];

	return requiredGates.every((gate) => gateStates[gate] === "PASS");
}

/**
 * Clear all gate states for a conversation (useful for restarting the cascade)
 *
 * @param ctx - Convex mutation context
 * @param conversationId - ID of the conversation
 */
export async function clearGateStates(
	ctx: MutationCtx,
	conversationId: Id<"conversations">,
): Promise<void> {
	await ctx.db.patch(conversationId, {
		gateStates: {},
		updatedAt: Date.now(),
	});
}

/**
 * Initialize gate states for a conversation with default PENDING values
 *
 * @param ctx - Convex mutation context
 * @param conversationId - ID of the conversation
 */
export async function initializeGateStates(
	ctx: MutationCtx,
	conversationId: Id<"conversations">,
): Promise<void> {
	const initialStates: Record<GateName, GateState> = {
		vagueness: "PENDING",
		focus: "PENDING",
		scope: "PENDING",
		specificity: "PENDING",
	};

	await setGateStates(ctx, conversationId, initialStates);
}

/**
 * Get the cascade version for a conversation (used for migration tracking)
 *
 * @param ctx - Convex query or mutation context
 * @param conversationId - ID of the conversation
 * @returns Cascade version number or 1 if not set
 */
export async function getCascadeVersion(
	ctx: QueryCtx | MutationCtx,
	conversationId: Id<"conversations">,
): Promise<number> {
	const conversation = await ctx.db.get(conversationId);
	if (!conversation) {
		throw new Error(`Conversation ${conversationId} not found`);
	}

	return conversation.cascadeVersion || 1;
}
