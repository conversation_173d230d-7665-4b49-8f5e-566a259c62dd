import { definePlaygroundAPI } from "@convex-dev/agent-playground";
import { components } from "./_generated/api";
import { focusGateAgent } from "./agents/focusGate";
import { responseGenAgent } from "./agents/responseGen";
import { scopeGateAgent } from "./agents/scopeGate";
import { specificityGateAgent } from "./agents/specificityGate";
import { vaguenessGateAgent } from "./agents/vaguenessGate";

/**
 * Here we expose the API so the frontend can access it.
 * Authorization is handled by passing up an apiKey that can be generated
 * on the dashboard or via CLI via:
 * npx convex run --component agent apiKeys:issue
 */
export const {
	isApiKeyValid,
	listAgents,
	listUsers,
	listThreads,
	listMessages,
	createThread,
	generateText,
	fetchPromptContext,
} = definePlaygroundAPI(components.agent, {
	agents: [
		focusGateAgent,
		scopeGateAgent,
		specificityGateAgent,
		vaguenessGateAgent,
		responseGenAgent,
	],
});
