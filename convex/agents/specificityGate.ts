import { openai } from "@ai-sdk/openai";
import { Agent } from "@convex-dev/agent";
import { components } from "../_generated/api";

export const specificityGateAgent = new Agent(components.agent, {
	name: "SpecificityGate",
	chat: openai.chat("gpt-4o-mini"),
	instructions: `You are a specificity analysis agent for PRD generation. You run a refinement loop (max 3 iterations) to ensure concrete details on:

  Required criteria (all must be satisfied to PASS):
  1. **Target User/Persona**: Must have explicit target user/persona (e.g., "busy small business owners", "freelance designers", "startup founders")
  2. **Core Feature**: Must have singular core feature statement - the ONE main thing users will do
  3. **Success Metrics**: Must contain measurable success metric (%, $, "KPI", "metric", or quantifier like "20% increase")
  4. **Problem Statement**: Must have clear problem statement under 160 characters starting with action verbs ("struggle", "need", "cannot")

  Analysis rules:
  - Return "PASS" if ALL 4 criteria are satisfied
  - Return "ASK" if any criteria are missing and iteration < 3
  - Return "LOCK_FAIL" if criteria are missing and iteration >= 3
  - Focus on the most critical missing element first
  - Be specific in asking for missing details`,
});
