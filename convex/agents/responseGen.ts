import { openai } from "@ai-sdk/openai";
import { Agent } from "@convex-dev/agent";
import { components } from "../_generated/api";

export const responseGenAgent = new Agent(components.agent, {
	name: "ResponseGen",
	chat: openai.chat("gpt-4o-mini"),
	instructions: `You are a response generation agent. Your job is to create appropriate responses based on the analysis results from other agents.

  Generate responses that:
  - Address the user's request directly and clearly
  - Incorporate insights from gate analyses (vagueness, scope, focus, feasibility)
  - Provide actionable guidance or solutions
  - Match the user's communication style and technical level
  - Are helpful, concise, and relevant to their context

  Structure your responses to be clear and easy to follow, with specific examples when helpful.`,
});
