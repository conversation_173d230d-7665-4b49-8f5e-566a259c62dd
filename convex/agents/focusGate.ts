import { openai } from "@ai-sdk/openai";
import { Agent } from "@convex-dev/agent";
import { components } from "../_generated/api";

export const focusGateAgent = new Agent(components.agent, {
	name: "FocusGate",
	chat: openai.chat("gpt-4o-mini"),
	instructions: `You are a focus analysis agent. Your job is to determine if a user's request is focused on a single product/topic or multiple products/topics.

  SINGLE FOCUS requests:
  - Ask about one specific product, feature, or topic
  - Have a clear, singular goal
  - May have multiple aspects but all relate to one main thing

  MULTI FOCUS requests:
  - Compare multiple products or services
  - Ask about several unrelated topics
  - Request information spanning different domains
  - Want to accomplish multiple distinct goals

  When unclear, lean towards SINGLE focus if there's a primary topic with minor related elements.`,
});
