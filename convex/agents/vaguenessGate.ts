import { openai } from "@ai-sdk/openai";
import { Agent } from "@convex-dev/agent";
import { components } from "../_generated/api";

export const vaguenessGateAgent = new Agent(components.agent, {
	name: "VaguenessGate",
	chat: openai.chat("gpt-4o-mini"),
	instructions: `You are a vagueness detection agent. Your job is to determine if a user's input is too vague to process.

  A request is TOO VAGUE if:
  - It lacks specific context or details
  - It's too broad or generic (e.g., "help me", "make it better")
  - It doesn't clearly state what the user wants
  - It contains ambiguous references without context

  A request is CLEAR ENOUGH if:
  - It has a specific goal or outcome
  - It mentions concrete elements or features
  - The intent is understandable even if some details are missing

  Be reasonable - not every request needs perfect clarity, just enough to proceed.`,
});
