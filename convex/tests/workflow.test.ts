import { expect, test, describe } from "vitest";
import {
	PRDWorkflowEngine,
	PRD_QUESTIONNAIRE_WORKFLOW,
} from "../lib/workflows/prd_questionnaire";

/**
 * Workflow State Machine Tests
 *
 * Tests for PRD questionnaire workflow progression and state management.
 */

describe("PRD Questionnaire Workflow", () => {
	test("should have valid workflow definition", () => {
		expect(PRD_QUESTIONNAIRE_WORKFLOW.length).toBeGreaterThan(0);

		// Check required steps exist
		const stepIds = PRD_QUESTIONNAIRE_WORKFLOW.map((step) => step.id);
		expect(stepIds).toContain("initialization");
		expect(stepIds).toContain("target_audience");
		expect(stepIds).toContain("problem_statement");
		expect(stepIds).toContain("completion");

		// Check all required steps have proper structure
		PRD_QUESTIONNAIRE_WORKFLOW.forEach((step) => {
			expect(step.id).toBeDefined();
			expect(step.name).toBeDefined();
			expect(step.description).toBeDefined();
			expect(Array.isArray(step.questions)).toBe(true);
			expect(Array.isArray(step.prerequisites)).toBe(true);
			expect(Array.isArray(step.nextSteps)).toBe(true);
			expect(typeof step.isRequired).toBe("boolean");

			// Check questions structure
			step.questions.forEach((question) => {
				expect(question.id).toBeDefined();
				expect(question.text).toBeDefined();
				expect(["menu", "text", "confirmation"]).toContain(question.type);
			});
		});
	});

	test("should initialize workflow engine correctly", () => {
		const engine = new PRDWorkflowEngine();
		const state = engine.getState();

		expect(state.currentStep).toBe("initialization");
		expect(state.completedSteps).toHaveLength(0);
		expect(state.responses).toEqual({});
		expect(state.progress.totalSteps).toBeGreaterThan(0);
		expect(state.progress.completedCount).toBe(0);
		expect(state.progress.percentComplete).toBe(0);
	});

	test("should get current step correctly", () => {
		const engine = new PRDWorkflowEngine();
		const currentStep = engine.getCurrentStep();

		expect(currentStep).toBeDefined();
		expect(currentStep?.id).toBe("initialization");
		expect(currentStep?.questions.length).toBeGreaterThan(0);
	});

	test("should process valid menu responses", () => {
		const engine = new PRDWorkflowEngine();
		const initialStep = engine.getCurrentStep();
		const firstQuestion = initialStep?.questions[0];

		expect(firstQuestion?.type).toBe("menu");
		expect(firstQuestion?.options?.length).toBeGreaterThan(0);

		// Process valid menu selection
		const result = engine.processResponse("welcome_choice", "start");

		expect(result.success).toBe(true);
		expect(result.nextStep).toBeDefined();

		const state = engine.getState();
		expect(state.responses.welcome_choice).toBe("start");
	});

	test("should validate required responses", () => {
		const engine = new PRDWorkflowEngine();

		// Try to submit empty response for required field
		const result = engine.processResponse("welcome_choice", "");

		expect(result.success).toBe(false);
		expect(result.validationErrors).toBeDefined();
		expect(result.validationErrors?.[0]).toContain("required");
	});

	test("should validate menu options", () => {
		const engine = new PRDWorkflowEngine();

		// Try invalid menu option
		const result = engine.processResponse("welcome_choice", "invalid_option");

		expect(result.success).toBe(false);
		expect(result.validationErrors).toBeDefined();
		expect(result.validationErrors?.[0]).toContain("valid option");
	});

	test("should progress through workflow steps", () => {
		const engine = new PRDWorkflowEngine();

		// Start with initialization
		expect(engine.getCurrentStep()?.id).toBe("initialization");
		expect(engine.isComplete()).toBe(false);

		// Process welcome choice
		const result = engine.processResponse("welcome_choice", "start");
		expect(result.success).toBe(true);

		// Should advance to target_audience
		const state = engine.getState();
		expect(state.currentStep).toBe("target_audience");
		expect(state.completedSteps).toContain("initialization");
		expect(state.progress.percentComplete).toBeGreaterThan(0);
	});

	test("should track progress correctly", () => {
		const engine = new PRDWorkflowEngine();
		const initialProgress = engine.getState().progress;

		// Complete first step
		engine.processResponse("welcome_choice", "start");

		const afterFirstStep = engine.getState().progress;
		expect(afterFirstStep.completedCount).toBeGreaterThan(
			initialProgress.completedCount,
		);
		expect(afterFirstStep.percentComplete).toBeGreaterThan(
			initialProgress.percentComplete,
		);
	});

	test("should get next question correctly", () => {
		const engine = new PRDWorkflowEngine();

		// Initially should get first question from initialization
		const firstQuestion = engine.getNextQuestion();
		expect(firstQuestion?.id).toBe("welcome_choice");

		// After answering, should get next question from new step
		engine.processResponse("welcome_choice", "start");
		const nextQuestion = engine.getNextQuestion();
		expect(nextQuestion?.id).toBe("audience_type");
	});

	test("should handle text input validation", () => {
		const engine = new PRDWorkflowEngine();

		// Navigate to a text input step
		engine.processResponse("welcome_choice", "start");
		engine.processResponse("audience_type", "b2b");

		// Move to problem statement (text input)
		const state = engine.getState();
		if (state.currentStep === "problem_statement") {
			// Test short text (should fail min length)
			const shortResult = engine.processResponse(
				"problem_description",
				"short",
			);
			expect(shortResult.success).toBe(false);
			expect(shortResult.validationErrors?.[0]).toContain("characters");

			// Test adequate length text
			const longText =
				"This is a comprehensive problem description that meets the minimum character requirements for the PRD questionnaire system.";
			const longResult = engine.processResponse(
				"problem_description",
				longText,
			);
			expect(longResult.success).toBe(true);
		}
	});

	test("should generate PRD content from responses", () => {
		const engine = new PRDWorkflowEngine({
			context: { projectName: "Test Project" },
			responses: {
				audience_type: "b2b",
				problem_description: "Users need better project management tools",
				solution_approach: "Build an AI-powered project management platform",
				feature_priority:
					"1. AI task scheduling 2. Team collaboration 3. Progress tracking",
				primary_metrics:
					"User adoption rate, task completion time, team satisfaction",
				platform_requirements: "web_only",
			},
		});

		const prd = engine.generatePRD();

		expect(prd).toContain("# Product Requirements Document");
		expect(prd).toContain("Test Project");
		expect(prd).toContain("b2b");
		expect(prd).toContain("better project management");
		expect(prd).toContain("AI-powered project management platform");
		expect(prd).toContain("AI task scheduling");
		expect(prd).toContain("User adoption rate");
		expect(prd).toContain("web_only");
		expect(prd).toContain("Generated by TheGeneral");
	});

	test("should handle workflow completion", () => {
		const engine = new PRDWorkflowEngine();

		// Simulate completing all required steps
		const requiredSteps = PRD_QUESTIONNAIRE_WORKFLOW.filter(
			(step) => step.isRequired,
		);

		// Mark all required steps as completed
		const state = engine.getState();
		state.completedSteps = requiredSteps.map((step) => step.id);
		state.progress.completedCount = requiredSteps.length;
		state.progress.percentComplete = 100;

		expect(engine.isComplete()).toBe(true);
	});

	test("should handle custom initialization state", () => {
		const customState = {
			currentStep: "target_audience",
			completedSteps: ["initialization"],
			responses: { welcome_choice: "start" },
			context: { projectName: "Custom Project" },
		};

		const engine = new PRDWorkflowEngine(customState);
		const state = engine.getState();

		expect(state.currentStep).toBe("target_audience");
		expect(state.completedSteps).toContain("initialization");
		expect(state.responses.welcome_choice).toBe("start");
		expect(state.context.projectName).toBe("Custom Project");
	});
});
