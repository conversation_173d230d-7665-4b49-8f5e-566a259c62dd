import { afterEach, beforeEach, describe, expect, test } from "vitest";

/**
 * Pricing Configuration Tests
 *
 * Tests for environment variable-based pricing configuration
 * for AI providers (Claude and OpenAI).
 */

// Helper function to simulate the parseEnvFloat behavior
function parseEnvFloat(
	envVar: string | undefined,
	defaultValue: number,
): number {
	if (!envVar) return defaultValue;
	const parsed = parseFloat(envVar);
	return Number.isNaN(parsed) ? defaultValue : parsed;
}

describe("Pricing Configuration Environment Variables", () => {
	let originalEnv: NodeJS.ProcessEnv;

	beforeEach(() => {
		// Save original environment
		originalEnv = { ...process.env };
	});

	afterEach(() => {
		// Restore original environment
		process.env = originalEnv;
	});

	describe("parseEnvFloat function behavior", () => {
		test("should return default value when env var is undefined", () => {
			const result = parseEnvFloat(undefined, 0.001);
			expect(result).toBe(0.001);
		});

		test("should return default value when env var is empty string", () => {
			const result = parseEnvFloat("", 0.002);
			expect(result).toBe(0.002);
		});

		test("should return parsed value when env var is valid number string", () => {
			const result = parseEnvFloat("0.0015", 0.001);
			expect(result).toBe(0.0015);
		});

		test("should return default value when env var is invalid string", () => {
			const result = parseEnvFloat("invalid", 0.003);
			expect(result).toBe(0.003);
		});

		test("should return default value when env var is NaN-producing string", () => {
			const result = parseEnvFloat("not-a-number", 0.004);
			expect(result).toBe(0.004);
		});

		test("should handle zero values correctly", () => {
			const result = parseEnvFloat("0", 0.005);
			expect(result).toBe(0);
		});

		test("should handle negative values correctly", () => {
			const result = parseEnvFloat("-0.001", 0.005);
			expect(result).toBe(-0.001);
		});

		test("should handle scientific notation", () => {
			const result = parseEnvFloat("1e-3", 0.005);
			expect(result).toBe(0.001);
		});
	});

	describe("Claude pricing environment variables", () => {
		test("should use CLAUDE_INPUT_COST_PER_1K when set", () => {
			process.env.CLAUDE_INPUT_COST_PER_1K = "0.001";
			const result = parseEnvFloat(
				process.env.CLAUDE_INPUT_COST_PER_1K,
				0.0008,
			);
			expect(result).toBe(0.001);
		});

		test("should use CLAUDE_OUTPUT_COST_PER_1K when set", () => {
			process.env.CLAUDE_OUTPUT_COST_PER_1K = "0.005";
			const result = parseEnvFloat(
				process.env.CLAUDE_OUTPUT_COST_PER_1K,
				0.004,
			);
			expect(result).toBe(0.005);
		});

		test("should fall back to defaults when Claude env vars are invalid", () => {
			process.env.CLAUDE_INPUT_COST_PER_1K = "invalid";
			process.env.CLAUDE_OUTPUT_COST_PER_1K = "";

			const inputResult = parseEnvFloat(
				process.env.CLAUDE_INPUT_COST_PER_1K,
				0.0008,
			);
			const outputResult = parseEnvFloat(
				process.env.CLAUDE_OUTPUT_COST_PER_1K,
				0.004,
			);

			expect(inputResult).toBe(0.0008);
			expect(outputResult).toBe(0.004);
		});
	});

	describe("OpenAI pricing environment variables", () => {
		test("should use OPENAI_INPUT_COST_PER_1K when set", () => {
			process.env.OPENAI_INPUT_COST_PER_1K = "0.0006";
			const result = parseEnvFloat(
				process.env.OPENAI_INPUT_COST_PER_1K,
				0.0005,
			);
			expect(result).toBe(0.0006);
		});

		test("should use OPENAI_OUTPUT_COST_PER_1K when set", () => {
			process.env.OPENAI_OUTPUT_COST_PER_1K = "0.0016";
			const result = parseEnvFloat(
				process.env.OPENAI_OUTPUT_COST_PER_1K,
				0.0015,
			);
			expect(result).toBe(0.0016);
		});

		test("should fall back to defaults when OpenAI env vars are invalid", () => {
			process.env.OPENAI_INPUT_COST_PER_1K = "not-a-number";
			process.env.OPENAI_OUTPUT_COST_PER_1K = "also-invalid";

			const inputResult = parseEnvFloat(
				process.env.OPENAI_INPUT_COST_PER_1K,
				0.0005,
			);
			const outputResult = parseEnvFloat(
				process.env.OPENAI_OUTPUT_COST_PER_1K,
				0.0015,
			);

			expect(inputResult).toBe(0.0005);
			expect(outputResult).toBe(0.0015);
		});
	});

	describe("Edge cases", () => {
		test("should handle very small numbers", () => {
			const result = parseEnvFloat("0.0000001", 0.001);
			expect(result).toBe(0.0000001);
		});

		test("should handle very large numbers", () => {
			const result = parseEnvFloat("999.999", 0.001);
			expect(result).toBe(999.999);
		});

		test("should handle whitespace in env vars", () => {
			const result = parseEnvFloat("  0.002  ", 0.001);
			expect(result).toBe(0.002);
		});

		test("should handle partial numbers", () => {
			const result = parseEnvFloat("0.001abc", 0.005);
			expect(result).toBe(0.001); // parseFloat stops at first non-numeric character
		});
	});
});
