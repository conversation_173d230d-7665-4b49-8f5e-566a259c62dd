import { expect, test, describe, vi } from "vitest";
import {
	SystemError,
	AIProviderError,
	AgentStateError,
	retryWithBackoff,
	validateAndSanitizeInput,
	withGracefulDegradation,
	RETRY_CONFIGS,
} from "../lib/errorHandling";

/**
 * Error Handling and Recovery Tests
 *
 * Tests for error handling, retry logic, and recovery mechanisms.
 */

describe("Error Handling System", () => {
	test("should create SystemError correctly", () => {
		const context = {
			operation: "test_operation",
			userId: "test-user",
			timestamp: Date.now(),
		};

		const error = new SystemError(
			"TEST_ERROR",
			"This is a test error",
			context,
			{ retryable: true, severity: "high" },
		);

		expect(error).toBeInstanceOf(Error);
		expect(error).toBeInstanceOf(SystemError);
		expect(error.code).toBe("TEST_ERROR");
		expect(error.message).toBe("This is a test error");
		expect(error.context).toBe(context);
		expect(error.retryable).toBe(true);
		expect(error.severity).toBe("high");
	});

	test("should create AIProviderError correctly", () => {
		const context = {
			operation: "ai_request",
			timestamp: Date.now(),
		};

		const error = new AIProviderError(
			"anthropic",
			"claude-3-haiku-20240307",
			"API rate limit exceeded",
			context,
			{ retryable: true },
		);

		expect(error).toBeInstanceOf(SystemError);
		expect(error).toBeInstanceOf(AIProviderError);
		expect(error.provider).toBe("anthropic");
		expect(error.model).toBe("claude-3-haiku-20240307");
		expect(error.severity).toBe("high");
	});

	test("should create AgentStateError correctly", () => {
		const context = {
			operation: "agent_workflow",
			agentId: "agent-123",
			timestamp: Date.now(),
		};

		const error = new AgentStateError(
			"TheGeneral",
			"target_audience",
			"Invalid workflow state",
			context,
		);

		expect(error).toBeInstanceOf(SystemError);
		expect(error).toBeInstanceOf(AgentStateError);
		expect(error.agentType).toBe("TheGeneral");
		expect(error.workflowStep).toBe("target_audience");
	});

	test("should have proper retry configurations", () => {
		expect(RETRY_CONFIGS.ai_request).toBeDefined();
		expect(RETRY_CONFIGS.database_operation).toBeDefined();
		expect(RETRY_CONFIGS.agent_initialization).toBeDefined();

		const aiConfig = RETRY_CONFIGS.ai_request;
		expect(aiConfig.maxAttempts).toBe(3);
		expect(aiConfig.baseDelay).toBe(1000);
		expect(aiConfig.backoffMultiplier).toBe(2);
		expect(aiConfig.retryableErrors).toContain("NETWORK_ERROR");
		expect(aiConfig.retryableErrors).toContain("RATE_LIMITED");
	});

	test("should retry successful operation", async () => {
		const operation = vi.fn().mockResolvedValue("success");
		const context = { operation: "test", timestamp: Date.now() };

		const result = await retryWithBackoff(
			operation,
			RETRY_CONFIGS.ai_request,
			context,
		);

		expect(result).toBe("success");
		expect(operation).toHaveBeenCalledTimes(1);
	});

	test("should retry failed operation with backoff", async () => {
		// Suppress console.warn during this test to avoid stderr noise
		const originalWarn = console.warn;
		console.warn = vi.fn();

		let attempts = 0;
		const operation = vi.fn().mockImplementation(async () => {
			attempts++;
			if (attempts < 3) {
				const error = new SystemError(
					"NETWORK_ERROR",
					"Network timeout",
					{ operation: "test", timestamp: Date.now() },
					{ retryable: true },
				);
				throw error;
			}
			return "success after retries";
		});

		const context = { operation: "test", timestamp: Date.now() };

		const result = await retryWithBackoff(
			operation,
			{
				...RETRY_CONFIGS.ai_request,
				maxAttempts: 3,
				baseDelay: 10,
				maxDelay: 50,
			}, // Reduce delay for test
			context,
		);

		expect(result).toBe("success after retries");
		expect(operation).toHaveBeenCalledTimes(3);

		// Restore console.warn
		console.warn = originalWarn;
	});

	test("should fail after max attempts", async () => {
		// Suppress console.warn during this test to avoid stderr noise
		const originalWarn = console.warn;
		console.warn = vi.fn();

		const operation = vi
			.fn()
			.mockRejectedValue(
				new SystemError(
					"NETWORK_ERROR",
					"Persistent network error",
					{ operation: "test", timestamp: Date.now() },
					{ retryable: true },
				),
			);

		const context = { operation: "test", timestamp: Date.now() };

		await expect(async () => {
			await retryWithBackoff(
				operation,
				{ ...RETRY_CONFIGS.ai_request, baseDelay: 10, maxDelay: 50 },
				context,
			);
		}).rejects.toThrowError("Persistent network error");

		expect(operation).toHaveBeenCalledTimes(3); // maxAttempts

		// Restore console.warn
		console.warn = originalWarn;
	});

	test("should not retry non-retryable errors", async () => {
		// Suppress all console output during this test to avoid stderr noise
		const originalError = console.error;
		const originalWarn = console.warn;
		const originalInfo = console.info;
		console.error = vi.fn();
		console.warn = vi.fn();
		console.info = vi.fn();

		const operation = vi
			.fn()
			.mockRejectedValue(
				new SystemError(
					"VALIDATION_ERROR",
					"Invalid input",
					{ operation: "test", timestamp: Date.now() },
					{ retryable: false },
				),
			);

		const context = { operation: "test", timestamp: Date.now() };

		await expect(async () => {
			await retryWithBackoff(
				operation,
				{ ...RETRY_CONFIGS.ai_request, baseDelay: 10, maxDelay: 50 },
				context,
			);
		}).rejects.toThrowError("Invalid input");

		expect(operation).toHaveBeenCalledTimes(1); // No retries

		// Restore console functions
		console.error = originalError;
		console.warn = originalWarn;
		console.info = originalInfo;
	});

	test("should validate and sanitize input correctly", () => {
		// Valid string input
		const validResult = validateAndSanitizeInput("Hello World", {
			required: true,
			type: "string",
			minLength: 5,
			maxLength: 20,
		});

		expect(validResult.isValid).toBe(true);
		expect(validResult.sanitized).toBe("Hello World");
		expect(validResult.errors).toHaveLength(0);
	});

	test("should validate required fields", () => {
		const result = validateAndSanitizeInput("", {
			required: true,
			type: "string",
		});

		expect(result.isValid).toBe(false);
		expect(result.errors).toContain("Field is required");
	});

	test("should validate string length", () => {
		const shortResult = validateAndSanitizeInput("Hi", {
			type: "string",
			minLength: 5,
		});

		expect(shortResult.isValid).toBe(false);
		expect(shortResult.errors).toContain("Minimum length is 5");

		const longResult = validateAndSanitizeInput("This is way too long", {
			type: "string",
			maxLength: 10,
		});

		expect(longResult.isValid).toBe(false);
		expect(longResult.errors).toContain("Maximum length is 10");
	});

	test("should validate pattern matching", () => {
		const result = validateAndSanitizeInput("invalid-email", {
			type: "string",
			pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
		});

		expect(result.isValid).toBe(false);
		expect(result.errors).toContain("Invalid format");

		const validResult = validateAndSanitizeInput("<EMAIL>", {
			type: "string",
			pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
		});

		expect(validResult.isValid).toBe(true);
	});

	test("should sanitize HTML content", () => {
		const maliciousInput =
			'<script>alert("xss")</script><p>Hello <b>World</b></p>';

		const result = validateAndSanitizeInput(maliciousInput, {
			type: "string",
			sanitize: true,
		});

		expect(result.isValid).toBe(true);
		expect(result.sanitized).not.toContain("<script>");
		expect(result.sanitized).not.toContain("<p>");
		expect(result.sanitized).not.toContain("<b>");
		expect(result.sanitized).toBe("Hello World");
	});

	test("should handle graceful degradation", async () => {
		// Suppress all console output during this test to avoid stderr noise
		const originalError = console.error;
		const originalWarn = console.warn;
		const originalInfo = console.info;
		console.error = vi.fn();
		console.warn = vi.fn();
		console.info = vi.fn();

		const failingOperation = vi
			.fn()
			.mockRejectedValue(new Error("Operation failed"));
		const fallbackValue = "fallback result";
		const context = { operation: "test", timestamp: Date.now() };

		const result = await withGracefulDegradation(
			failingOperation,
			fallbackValue,
			context,
		);

		expect(result).toBe(fallbackValue);
		expect(failingOperation).toHaveBeenCalledTimes(1);

		// Restore console functions
		console.error = originalError;
		console.warn = originalWarn;
		console.info = originalInfo;
	});

	test("should return primary result when successful", async () => {
		const successfulOperation = vi.fn().mockResolvedValue("primary result");
		const fallbackValue = "fallback result";
		const context = { operation: "test", timestamp: Date.now() };

		const result = await withGracefulDegradation(
			successfulOperation,
			fallbackValue,
			context,
		);

		expect(result).toBe("primary result");
		expect(successfulOperation).toHaveBeenCalledTimes(1);
	});

	test("should validate type checking", () => {
		const numberResult = validateAndSanitizeInput("123", {
			type: "number",
		});

		expect(numberResult.isValid).toBe(false);
		expect(numberResult.errors).toContain("Expected number, got string");

		const validNumberResult = validateAndSanitizeInput(123, {
			type: "number",
		});

		expect(validNumberResult.isValid).toBe(true);
	});

	test("should handle optional fields", () => {
		const result = validateAndSanitizeInput(undefined, {
			required: false,
			type: "string",
			minLength: 5,
		});

		expect(result.isValid).toBe(true);
		expect(result.sanitized).toBeUndefined();
		expect(result.errors).toHaveLength(0);
	});

	test("should handle null and empty values consistently", () => {
		const nullResult = validateAndSanitizeInput(null, {
			required: false,
			type: "string",
		});

		const emptyResult = validateAndSanitizeInput("", {
			required: false,
			type: "string",
		});

		expect(nullResult.isValid).toBe(true);
		expect(emptyResult.isValid).toBe(true);
	});
});
