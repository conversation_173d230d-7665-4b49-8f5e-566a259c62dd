import { convexTest } from "convex-test";
import { expect, test, describe } from "vitest";
import schema from "../schema";
import { api } from "../_generated/api";

// Import all Convex function modules for testing
const modules = import.meta.glob("../**/!(*.*.*)*.*s");

/**
 * TheGeneral Agent Tests
 *
 * Tests for agent initialization, messaging, and personality consistency.
 */

describe("TheGeneral Agent Core", () => {
	test("should initialize TheGeneral agent", async () => {
		const t = convexTest(schema, modules);

		// Create PRD session first
		const sessionId = await t.mutation(api.functions.prd.createSession, {
			userId: "test-user-123",
			projectName: "Test Project",
		});

		// Initialize agent
		const agentId = await t.mutation(api.functions.agents.initializeGeneral, {
			sessionId,
		});

		expect(agentId).toBeDefined();

		// Verify agent was created
		const agent = await t.query(api.functions.agents.getAgentBySession, {
			sessionId,
		});

		expect(agent).toBeDefined();
		expect(agent?.type).toBe("TheGeneral");
		expect(agent?.personality).toBe("drill_instructor");
		expect(agent?.status).toBe("active");
		expect(agent?.currentWorkflow).toBe("prd_questionnaire");
		expect(agent?.context.projectName).toBe("Test Project");

		// Check that greeting message was created
		const conversation = await t.query(api.functions.agents.getConversation, {
			sessionId,
		});
		expect(conversation.messages).toHaveLength(1);
		expect(conversation.messages[0].senderType).toBe("agent");
		expect(conversation.messages[0].senderId).toBe("TheGeneral");
		expect(conversation.messages[0].messageType).toBe("greeting");
		expect(conversation.messages[0].content).toContain("LISTEN UP, RECRUIT!");
	});

	test("should send message and trigger agent response", async () => {
		const t = convexTest(schema, modules);

		// Setup session and agent
		const sessionId = await t.mutation(api.functions.prd.createSession, {
			userId: "test-user-123",
			projectName: "Test Project",
		});

		await t.mutation(api.functions.agents.initializeGeneral, { sessionId });

		// Send user message
		const response = await t.mutation(api.functions.agents.sendMessage, {
			sessionId,
			senderId: "test-user-123",
			content: "1",
			messageType: "response",
		});

		expect(response.sessionId).toBe(sessionId);
		expect(response.agentResponse).toBeDefined();
		expect(response.workflowStep).toBeDefined();

		// Check conversation history
		const conversation = await t.query(api.functions.agents.getConversation, {
			sessionId,
		});
		expect(conversation.messages.length).toBeGreaterThan(1);

		// Find user message
		const userMessage = conversation.messages.find(
			(m) => m.senderType === "user",
		);
		expect(userMessage?.content).toBe("1");

		// Find agent response (should be the last message)
		const agentResponse =
			conversation.messages[conversation.messages.length - 1];
		expect(agentResponse.senderType).toBe("agent");
		expect(agentResponse.senderId).toBe("TheGeneral");
	});

	test("should update agent status", async () => {
		const t = convexTest(schema, modules);

		const sessionId = await t.mutation(api.functions.prd.createSession, {
			userId: "test-user-123",
			projectName: "Test Project",
		});

		const agentId = await t.mutation(api.functions.agents.initializeGeneral, {
			sessionId,
		});

		// Update agent status
		await t.mutation(api.functions.agents.updateAgentStatus, {
			agentId,
			status: "paused",
		});

		const agent = await t.query(api.functions.agents.getAgentBySession, {
			sessionId,
		});
		expect(agent?.status).toBe("paused");
	});

	test("should maintain conversation context", async () => {
		const t = convexTest(schema, modules);

		const sessionId = await t.mutation(api.functions.prd.createSession, {
			userId: "test-user-123",
			projectName: "Test Project",
		});

		await t.mutation(api.functions.agents.initializeGeneral, { sessionId });

		// Send multiple messages
		await t.mutation(api.functions.agents.sendMessage, {
			sessionId,
			senderId: "test-user-123",
			content: "1",
		});

		await t.mutation(api.functions.agents.sendMessage, {
			sessionId,
			senderId: "test-user-123",
			content: "B2B professionals",
		});

		const conversation = await t.query(api.functions.agents.getConversation, {
			sessionId,
		});

		// Should have greeting + 2 user messages + 2 agent responses
		expect(conversation.messages.length).toBeGreaterThanOrEqual(5);

		// Messages should be in chronological order by sequence number
		const sequenceNumbers = conversation.messages.map((m) => m.sequenceNumber);
		const sortedSequenceNumbers = [...sequenceNumbers].sort((a, b) => a - b);
		expect(sequenceNumbers).toEqual(sortedSequenceNumbers);

		// Verify sequence numbers are sequential
		for (let i = 0; i < sequenceNumbers.length; i++) {
			expect(sequenceNumbers[i]).toBe(i + 1);
		}
	});
});
