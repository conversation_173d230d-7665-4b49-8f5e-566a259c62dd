import { convexTest } from "convex-test";
import { describe, expect, test } from "vitest";
import { api } from "../_generated/api";
import schema from "../schema";
import { PRD_QUESTIONNAIRE_WORKFLOW } from "../lib/workflows/prd_questionnaire";

// Import all Convex function modules for testing
const modules = import.meta.glob("../**/!(*.*.*)*.*s");

/**
 * PRD Session Management Tests
 *
 * Tests for PRD session creation, management, and progression.
 */

describe("PRD Session Management", () => {
	test("should create a new PRD session", async () => {
		const t = convexTest(schema, modules);

		const sessionId = await t.mutation(api.functions.prd.createSession, {
			userId: "test-user-123",
			projectName: "Test Project",
			targetAudience: "B2B professionals",
		});

		expect(sessionId).toBeDefined();

		// Verify session was created correctly
		const session = await t.query(api.functions.prd.getSession, { sessionId });

		expect(session?.session).toBeDefined();
		expect(session?.session.userId).toBe("test-user-123");
		expect(session?.session.projectName).toBe("Test Project");
		expect(session?.session.status).toBe("active");
		expect(session?.session.currentStep).toBe("initialization");
		expect(session?.session.progress.percentComplete).toBe(0);
		expect(session?.session.progress.totalSteps).toBe(
			PRD_QUESTIONNAIRE_WORKFLOW.filter((step) => step.isRequired).length,
		);
	});

	test("should reject empty project names", async () => {
		const t = convexTest(schema, modules);

		// Test empty string
		await expect(
			t.mutation(api.functions.prd.createSession, {
				userId: "test-user-123",
				projectName: "",
				targetAudience: "B2B professionals",
			}),
		).rejects.toThrow("Project name cannot be empty");

		// Test whitespace only
		await expect(
			t.mutation(api.functions.prd.createSession, {
				userId: "test-user-123",
				projectName: "   ",
				targetAudience: "B2B professionals",
			}),
		).rejects.toThrow("Project name cannot be empty");
	});

	test("should trim project name whitespace", async () => {
		const t = convexTest(schema, modules);

		const sessionId = await t.mutation(api.functions.prd.createSession, {
			userId: "test-user-123",
			projectName: "  Test Project  ",
			targetAudience: "B2B professionals",
		});

		const session = await t.query(api.functions.prd.getSession, { sessionId });
		expect(session?.session.projectName).toBe("Test Project");
	});

	test("should update questionnaire step and progress", async () => {
		const t = convexTest(schema, modules);

		// Create session
		const sessionId = await t.mutation(api.functions.prd.createSession, {
			userId: "test-user-123",
			projectName: "Test Project",
		});

		// Update first step
		const result = await t.mutation(api.functions.prd.updateQuestionnaireStep, {
			sessionId,
			stepName: "target_audience",
			stepData: { type: "b2b", details: "Enterprise customers" },
			markCompleted: true,
		});

		expect(result.stepName).toBe("target_audience");
		expect(result.progress.completedSteps).toContain("target_audience");
		expect(result.progress.percentComplete).toBeGreaterThan(0);

		// Verify session was updated
		const session = await t.query(api.functions.prd.getSession, { sessionId });
		expect(session?.session.currentStep).toBe("target_audience");
		expect(session?.session.questionnaire.target_audience).toEqual({
			type: "b2b",
			details: "Enterprise customers",
		});
	});

	test("should get user sessions", async () => {
		const t = convexTest(schema, modules);

		const userId = "test-user-456";

		// Create multiple sessions
		await t.mutation(api.functions.prd.createSession, {
			userId,
			projectName: "Project 1",
		});

		await t.mutation(api.functions.prd.createSession, {
			userId,
			projectName: "Project 2",
		});

		// Get user sessions
		const sessions = await t.query(api.functions.prd.getUserSessions, {
			userId,
		});

		expect(sessions).toHaveLength(2);
		expect(sessions.map((s) => s.projectName)).toContain("Project 1");
		expect(sessions.map((s) => s.projectName)).toContain("Project 2");
	});

	test("should update session status", async () => {
		const t = convexTest(schema, modules);

		const sessionId = await t.mutation(api.functions.prd.createSession, {
			userId: "test-user-123",
			projectName: "Test Project",
		});

		// Update status to completed
		await t.mutation(api.functions.prd.updateSessionStatus, {
			sessionId,
			status: "completed",
		});

		const session = await t.query(api.functions.prd.getSession, { sessionId });
		expect(session?.session.status).toBe("completed");
	});

	test("should add generated PRD content", async () => {
		const t = convexTest(schema, modules);

		const sessionId = await t.mutation(api.functions.prd.createSession, {
			userId: "test-user-123",
			projectName: "Test Project",
		});

		const prdContent = "# Test PRD\n\nThis is a test PRD document.";

		await t.mutation(api.functions.prd.addGeneratedPRD, {
			sessionId,
			content: prdContent,
			format: "markdown",
			version: "1.0",
		});

		const session = await t.query(api.functions.prd.getSession, { sessionId });
		expect(session?.session.generatedPRD?.content).toBe(prdContent);
		expect(session?.session.generatedPRD?.format).toBe("markdown");
		expect(session?.session.status).toBe("completed");
	});

	test("should reject PRD content that exceeds maximum size", async () => {
		const t = convexTest(schema, modules);

		const sessionId = await t.mutation(api.functions.prd.createSession, {
			userId: "test-user-123",
			projectName: "Test Project",
		});

		// Create content that exceeds 1MB (1,048,576 bytes)
		const largeContent = "A".repeat(1048577); // 1MB + 1 byte

		await expect(
			t.mutation(api.functions.prd.addGeneratedPRD, {
				sessionId,
				content: largeContent,
				format: "markdown",
				version: "1.0",
			}),
		).rejects.toThrow(/PRD content size.*exceeds maximum allowed size/);
	});

	test("should accept PRD content within size limits", async () => {
		const t = convexTest(schema, modules);

		const sessionId = await t.mutation(api.functions.prd.createSession, {
			userId: "test-user-123",
			projectName: "Test Project",
		});

		// Create content that is exactly at the limit (1MB)
		const maxSizeContent = "A".repeat(1048576); // Exactly 1MB

		await expect(
			t.mutation(api.functions.prd.addGeneratedPRD, {
				sessionId,
				content: maxSizeContent,
				format: "markdown",
				version: "1.0",
			}),
		).resolves.not.toThrow();

		const session = await t.query(api.functions.prd.getSession, { sessionId });
		expect(session?.session.generatedPRD?.content).toBe(maxSizeContent);
		expect(session?.session.status).toBe("completed");
	});

	test("should provide helpful error message for oversized content", async () => {
		const t = convexTest(schema, modules);

		const sessionId = await t.mutation(api.functions.prd.createSession, {
			userId: "test-user-123",
			projectName: "Test Project",
		});

		// Create content that is 2MB (double the limit)
		const oversizedContent = "A".repeat(2097152); // 2MB

		await expect(
			t.mutation(api.functions.prd.addGeneratedPRD, {
				sessionId,
				content: oversizedContent,
				format: "markdown",
				version: "1.0",
			}),
		).rejects.toThrow(
			"PRD content size (2048KB) exceeds maximum allowed size (1024KB)",
		);
	});
});
