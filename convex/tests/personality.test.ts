import { expect, test, describe } from "vitest";
import {
	GENERAL_PERSONALITY,
	generateSystemPrompt,
	formatPersonalityResponse,
	validatePersonalityConsistency,
	getRandomPhrase,
	getWorkflowEmoji,
} from "../lib/ai/personality";

/**
 * Agent Personality Consistency Tests
 *
 * Tests for maintaining drill instructor character voice
 * and personality consistency across interactions.
 */

describe("TheGeneral Personality System", () => {
	test("should have proper personality configuration", () => {
		expect(GENERAL_PERSONALITY.name).toBe("General Thompson");
		expect(GENERAL_PERSONALITY.role).toBe(
			"Drill Instructor & PRD Creation Specialist",
		);

		// Check vocabulary arrays are populated
		expect(GENERAL_PERSONALITY.vocabulary.greetings.length).toBeGreaterThan(0);
		expect(
			GENERAL_PERSONALITY.vocabulary.acknowledgments.length,
		).toBeGreaterThan(0);
		expect(GENERAL_PERSONALITY.vocabulary.corrections.length).toBeGreaterThan(
			0,
		);
		expect(GENERAL_PERSONALITY.vocabulary.motivations.length).toBeGreaterThan(
			0,
		);

		// Check emojis are available
		expect(GENERAL_PERSONALITY.emojis.length).toBeGreaterThan(0);
		expect(GENERAL_PERSONALITY.emojis).toContain("🎯");
		expect(GENERAL_PERSONALITY.emojis).toContain("🪖");
	});

	test("should generate proper system prompt", () => {
		const context = {
			projectName: "Test Project",
			currentStep: "initialization",
			userName: "TestUser",
		};

		const systemPrompt = generateSystemPrompt(GENERAL_PERSONALITY, context);

		expect(systemPrompt).toContain("General Thompson");
		expect(systemPrompt).toContain("Drill Instructor");
		expect(systemPrompt).toContain("Test Project");
		expect(systemPrompt).toContain("initialization");
		expect(systemPrompt).toContain("TestUser");
		expect(systemPrompt).toContain("Military terminology and commands");
		expect(systemPrompt).toContain("ALL CAPS");
	});

	test("should format responses with personality consistency", () => {
		const content = "This is a test response with instructions.";

		const greeting = formatPersonalityResponse(content, "greeting");
		expect(greeting).toMatch(
			/LISTEN UP|ATTENTION|FALL IN|TIME TO GET|READY FOR/,
		);
		expect(greeting).toContain("🎯");
		expect(greeting).toMatch(
			/MOVE|TIME IS|SHORTCUTS|PRECISION|FOCUSED|PUSHING|BUILDING/,
		);

		const acknowledgment = formatPersonalityResponse(content, "acknowledgment");
		expect(acknowledgment).toMatch(
			/OUTSTANDING|EXCELLENT|THINKING|SPIRIT|INTEL|SOLID|MISSION/,
		);
		expect(acknowledgment).toContain("🏆");

		const correction = formatPersonalityResponse(content, "correction");
		expect(correction).toMatch(
			/NEGATIVE|NOT ACCEPTABLE|MORE DETAILS|SPECIFIC|FOCUS|INSUFFICIENT|CLARIFY/,
		);
		expect(correction).toContain("⚡");
	});

	test("should validate personality consistency", () => {
		// Good response with military terms, caps, options, emojis
		const goodResponse =
			"OUTSTANDING, RECRUIT! 🎯\n\nNow we need your TARGET AUDIENCE intel:\n\n**[1]** B2B professionals\n**[2]** B2C consumers\n\nPICK YOUR NUMBER! 🪖";

		const goodValidation = validatePersonalityConsistency(goodResponse);
		expect(goodValidation.isConsistent).toBe(true);
		expect(goodValidation.issues).toHaveLength(0);

		// Bad response missing key elements
		const badResponse =
			"okay, so what's your target audience? please tell me more about who will use this.";

		const badValidation = validatePersonalityConsistency(badResponse);
		expect(badValidation.isConsistent).toBe(false);
		expect(badValidation.issues.length).toBeGreaterThan(0);
		expect(badValidation.suggestions.length).toBeGreaterThan(0);
	});

	test("should get random phrases consistently", () => {
		const greeting1 = getRandomPhrase("greetings");
		const greeting2 = getRandomPhrase("greetings");

		expect(GENERAL_PERSONALITY.vocabulary.greetings).toContain(greeting1);
		expect(GENERAL_PERSONALITY.vocabulary.greetings).toContain(greeting2);

		const ack1 = getRandomPhrase("acknowledgments");
		expect(GENERAL_PERSONALITY.vocabulary.acknowledgments).toContain(ack1);
	});

	test("should provide workflow-appropriate emojis", () => {
		expect(getWorkflowEmoji("initialization")).toBe("🎯");
		expect(getWorkflowEmoji("target_audience")).toBe("👥");
		expect(getWorkflowEmoji("requirements")).toBe("📋");
		expect(getWorkflowEmoji("completion")).toBe("🏆");
		expect(getWorkflowEmoji("unknown_step")).toBe("📋"); // default
	});

	test("should maintain character voice in different scenarios", () => {
		const scenarios = [
			{ type: "greeting" as const, content: "Welcome to PRD creation!" },
			{ type: "question" as const, content: "What is your target audience?" },
			{
				type: "acknowledgment" as const,
				content: "Good answer about B2B customers.",
			},
			{
				type: "correction" as const,
				content: "That answer needs more detail.",
			},
			{ type: "completion" as const, content: "PRD creation is complete!" },
		];

		scenarios.forEach((scenario) => {
			const formatted = formatPersonalityResponse(
				scenario.content,
				scenario.type,
			);

			// All responses should maintain military character
			const validation = validatePersonalityConsistency(formatted);

			if (!validation.isConsistent) {
				console.log(`Failed scenario: ${scenario.type}`);
				console.log(`Response: ${formatted}`);
				console.log(`Issues: ${validation.issues.join(", ")}`);
			}

			// Some responses may not be fully consistent due to content constraints
			// but should at least have some personality elements
			expect(formatted.length).toBeGreaterThan(scenario.content.length);
		});
	});

	test("should handle edge cases gracefully", () => {
		// Empty content
		const emptyResponse = formatPersonalityResponse("", "greeting");
		expect(emptyResponse.length).toBeGreaterThan(0);
		expect(emptyResponse).toContain("🎯");

		// Very long content
		const longContent = "A".repeat(1000);
		const longResponse = formatPersonalityResponse(longContent, "question");
		expect(longResponse).toContain(longContent);
		expect(longResponse).toContain("🎯");

		// Special characters
		const specialContent = "What about $pecial ch@racters & symbols?";
		const specialResponse = formatPersonalityResponse(
			specialContent,
			"acknowledgment",
		);
		expect(specialResponse).toContain(specialContent);
	});
});
