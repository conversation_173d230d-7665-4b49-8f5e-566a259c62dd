import { describe, expect, test, vi } from "vitest";
import {
	CLAUDE_PRICING,
	calculateClaudeCost,
	generateClaudeResponse,
} from "../lib/ai/anthropic";
import {
	calculateOpenAICost,
	generateOpenAIResponse,
	OPENAI_PRICING,
} from "../lib/ai/openai";
import { validateUsageData } from "../lib/ai/utils";

/**
 * AI Integration Tests
 *
 * Tests for AI provider integrations focusing on usage data validation
 * and graceful handling of missing properties.
 */

// Mock the AI SDK functions
vi.mock("ai", () => ({
	generateText: vi.fn(),
}));

vi.mock("@ai-sdk/anthropic", () => ({
	anthropic: vi.fn(() => "mocked-model"),
}));

vi.mock("@ai-sdk/openai", () => ({
	openai: vi.fn(() => "mocked-model"),
}));

describe("AI Integration Usage Validation", () => {
	test("<PERSON> should handle missing usage data gracefully", async () => {
		const { generateText } = await import("ai");
		const mockedGenerateText = vi.mocked(generateText);

		// Mock response with missing usage data
		mockedGenerateText.mockResolvedValueOnce({
			text: "Test response",
			// usage property is completely missing
		} as any);

		const result = await generateClaudeResponse(
			"Test prompt",
			"Test system prompt",
		);

		expect(result.content).toBe("Test response");
		expect(result.tokensUsed.prompt).toBe(0);
		expect(result.tokensUsed.completion).toBe(0);
		expect(result.tokensUsed.total).toBe(0);
		expect(typeof result.responseTime).toBe("number");
	});

	test("Claude should handle partial usage data gracefully", async () => {
		const { generateText } = await import("ai");
		const mockedGenerateText = vi.mocked(generateText);

		// Mock response with partial usage data
		mockedGenerateText.mockResolvedValueOnce({
			text: "Test response",
			usage: {
				promptTokens: 10,
				// completionTokens is missing
				// totalTokens is missing
			},
		} as any);

		const result = await generateClaudeResponse(
			"Test prompt",
			"Test system prompt",
		);

		expect(result.content).toBe("Test response");
		expect(result.tokensUsed.prompt).toBe(10);
		expect(result.tokensUsed.completion).toBe(0);
		expect(result.tokensUsed.total).toBe(10); // Should calculate from prompt + completion
	});

	test("Claude should handle complete usage data correctly", async () => {
		const { generateText } = await import("ai");
		const mockedGenerateText = vi.mocked(generateText);

		// Mock response with complete usage data
		mockedGenerateText.mockResolvedValueOnce({
			text: "Test response",
			usage: {
				promptTokens: 10,
				completionTokens: 15,
				totalTokens: 25,
			},
		} as any);

		const result = await generateClaudeResponse(
			"Test prompt",
			"Test system prompt",
		);

		expect(result.content).toBe("Test response");
		expect(result.tokensUsed.prompt).toBe(10);
		expect(result.tokensUsed.completion).toBe(15);
		expect(result.tokensUsed.total).toBe(25);
	});

	test("OpenAI should handle missing usage data gracefully", async () => {
		const { generateText } = await import("ai");
		const mockedGenerateText = vi.mocked(generateText);

		// Mock response with missing usage data
		mockedGenerateText.mockResolvedValueOnce({
			text: "Test response",
			// usage property is completely missing
		} as any);

		const result = await generateOpenAIResponse(
			"Test prompt",
			"Test system prompt",
		);

		expect(result.content).toBe("Test response");
		expect(result.tokensUsed.prompt).toBe(0);
		expect(result.tokensUsed.completion).toBe(0);
		expect(result.tokensUsed.total).toBe(0);
		expect(typeof result.responseTime).toBe("number");
	});

	test("OpenAI should calculate total tokens when missing", async () => {
		const { generateText } = await import("ai");
		const mockedGenerateText = vi.mocked(generateText);

		// Mock response with prompt and completion tokens but missing total
		mockedGenerateText.mockResolvedValueOnce({
			text: "Test response",
			usage: {
				promptTokens: 20,
				completionTokens: 30,
				// totalTokens is missing - should be calculated
			},
		} as any);

		const result = await generateOpenAIResponse(
			"Test prompt",
			"Test system prompt",
		);

		expect(result.content).toBe("Test response");
		expect(result.tokensUsed.prompt).toBe(20);
		expect(result.tokensUsed.completion).toBe(30);
		expect(result.tokensUsed.total).toBe(50); // Should calculate: 20 + 30
	});

	describe("validateUsageData utility", () => {
		test("should handle completely missing usage data", () => {
			const result = validateUsageData();

			expect(result.prompt).toBe(0);
			expect(result.completion).toBe(0);
			expect(result.total).toBe(0);
		});

		test("should handle undefined usage object", () => {
			const result = validateUsageData(undefined);

			expect(result.prompt).toBe(0);
			expect(result.completion).toBe(0);
			expect(result.total).toBe(0);
		});

		test("should handle partial usage data", () => {
			const result = validateUsageData({
				promptTokens: 15,
				// completionTokens missing
				// totalTokens missing
			});

			expect(result.prompt).toBe(15);
			expect(result.completion).toBe(0);
			expect(result.total).toBe(15); // Should calculate from prompt + completion
		});

		test("should preserve provided totalTokens over calculation", () => {
			const result = validateUsageData({
				promptTokens: 10,
				completionTokens: 20,
				totalTokens: 35, // Different from 10 + 20 = 30
			});

			expect(result.prompt).toBe(10);
			expect(result.completion).toBe(20);
			expect(result.total).toBe(35); // Should use provided value, not calculated
		});

		test("should handle zero values correctly", () => {
			const result = validateUsageData({
				promptTokens: 0,
				completionTokens: 0,
				totalTokens: 0,
			});

			expect(result.prompt).toBe(0);
			expect(result.completion).toBe(0);
			expect(result.total).toBe(0);
		});
	});

	describe("Pricing Configuration", () => {
		test("Claude pricing should have correct default values", () => {
			// Test that the current imported pricing config has the expected defaults
			expect(CLAUDE_PRICING.inputCostPer1K).toBe(0.0008);
			expect(CLAUDE_PRICING.outputCostPer1K).toBe(0.004);

			// Verify they are valid numbers
			expect(typeof CLAUDE_PRICING.inputCostPer1K).toBe("number");
			expect(typeof CLAUDE_PRICING.outputCostPer1K).toBe("number");
			expect(Number.isNaN(CLAUDE_PRICING.inputCostPer1K)).toBe(false);
			expect(Number.isNaN(CLAUDE_PRICING.outputCostPer1K)).toBe(false);
		});

		test("OpenAI pricing should have correct default values", () => {
			// Test that the current imported pricing config has the expected defaults
			expect(OPENAI_PRICING.inputCostPer1K).toBe(0.0005);
			expect(OPENAI_PRICING.outputCostPer1K).toBe(0.0015);

			// Verify they are valid numbers
			expect(typeof OPENAI_PRICING.inputCostPer1K).toBe("number");
			expect(typeof OPENAI_PRICING.outputCostPer1K).toBe("number");
			expect(Number.isNaN(OPENAI_PRICING.inputCostPer1K)).toBe(false);
			expect(Number.isNaN(OPENAI_PRICING.outputCostPer1K)).toBe(false);
		});

		test("Claude cost calculation should use updated pricing", () => {
			const tokensUsed = {
				prompt: 1000,
				completion: 500,
				total: 1500,
			};

			const cost = calculateClaudeCost(tokensUsed);

			// With default rates: (1000/1000 * 0.0008) + (500/1000 * 0.004) = 0.0008 + 0.002 = 0.0028
			expect(cost).toBe(0.0028);
		});

		test("OpenAI cost calculation should use updated pricing", () => {
			const tokensUsed = {
				prompt: 2000,
				completion: 1000,
				total: 3000,
			};

			const cost = calculateOpenAICost(tokensUsed);

			// With default rates: (2000/1000 * 0.0005) + (1000/1000 * 0.0015) = 0.001 + 0.0015 = 0.0025
			expect(cost).toBe(0.0025);
		});
	});
});
