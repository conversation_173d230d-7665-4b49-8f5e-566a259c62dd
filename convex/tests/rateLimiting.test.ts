import { convexTest } from "convex-test";
import { expect, test, describe } from "vitest";
import schema from "../schema";
import {
	RATE_LIMIT_CONFIGS,
	createRateLimiter,
	trackCost,
	getUsageStats,
	checkUsageLimits,
} from "../lib/rateLimiting";

// Import all Convex function modules for testing
const modules = import.meta.glob("../**/!(*.*.*)*.*s");

/**
 * Rate Limiting and Cost Tracking Tests
 *
 * Tests for rate limiting functionality and usage monitoring.
 */

describe("Rate Limiting System", () => {
	test("should have proper rate limit configurations", () => {
		expect(RATE_LIMIT_CONFIGS.ai_message).toBeDefined();
		expect(RATE_LIMIT_CONFIGS.session_creation).toBeDefined();
		expect(RATE_LIMIT_CONFIGS.message_send).toBeDefined();
		expect(RATE_LIMIT_CONFIGS.api_call).toBeDefined();

		// Check AI message config (most restrictive)
		const aiConfig = RATE_LIMIT_CONFIGS.ai_message;
		expect(aiConfig.rate.count).toBe(10);
		expect(aiConfig.rate.period).toBe(60 * 60 * 1000); // 1 hour
		expect(aiConfig.kind).toBe("token bucket");
		expect(aiConfig.capacity).toBe(15);
	});

	test("should create rate limiter instances", () => {
		const config = RATE_LIMIT_CONFIGS.ai_message;
		const limiter = createRateLimiter(config);

		expect(limiter).toBeDefined();
		// Note: Actual rate limiting would require Convex context
	});

	test("should track cost metrics", async () => {
		const t = convexTest(schema, modules);

		const metrics = {
			operation: "ai_message_generation",
			provider: "anthropic" as const,
			model: "claude-3-haiku-20240307",
			tokensUsed: {
				prompt: 100,
				completion: 200,
				total: 300,
			},
			cost: 0.005, // $0.005
			responseTime: 1500,
			timestamp: Date.now(),
			sessionId: "test-session-456",
		};

		await t.run(async (ctx) => {
			await trackCost(ctx as any, metrics);
		});

		// Verify usage tracking record was created
		const usageRecords = await t.run((ctx) =>
			ctx.db.query("usageTracking").collect(),
		);
		expect(usageRecords.length).toBeGreaterThan(0);

		const record = usageRecords[0];
		expect(record.userId).toBeUndefined(); // No userId provided
		expect(record.sessionId).toBe("test-session-456");
		expect(record.action).toBe("api_call");
		expect(record.count).toBe(1);
		expect(record.tokenCost).toBe(1); // Stored as cents: Math.round(0.005 * 100) = Math.round(0.5) = 1
	});

	test("should get usage statistics", async () => {
		const t = convexTest(schema, modules);

		const now = new Date();
		const today = now.toISOString().slice(0, 10);

		// Insert test usage data
		await t.run(async (ctx) => {
			await ctx.db.insert("usageTracking", {
				action: "api_call",
				dayWindow: today,
				hourWindow: now.toISOString().slice(0, 13),
				monthWindow: now.toISOString().slice(0, 7),
				count: 1,
				tokenCost: 50, // 50 cents
				createdAt: now.getTime(),
				updatedAt: now.getTime(),
			});
		});

		await t.run(async (ctx) => {
			await ctx.db.insert("usageTracking", {
				action: "api_call",
				dayWindow: today,
				hourWindow: now.toISOString().slice(0, 13),
				monthWindow: now.toISOString().slice(0, 7),
				count: 1,
				tokenCost: 30, // 30 cents
				createdAt: now.getTime(),
				updatedAt: now.getTime(),
			});
		});

		const stats = await t.run((ctx) => getUsageStats(ctx as any, {}, "day"));

		expect(stats.requestCount).toBe(2);
		expect(stats.totalCost).toBe(0.8); // (50 + 30) / 100
		expect(stats.averageCost).toBe(0.4); // 0.8 / 2
		expect(stats.tokenUsage).toBe(2000); // Rough estimate
	});

	test("should check usage limits", async () => {
		const t = convexTest(schema, modules);

		const now = new Date();
		const today = now.toISOString().slice(0, 10);

		// Insert usage data approaching limits
		for (let i = 0; i < 85; i++) {
			await t.run(async (ctx) => {
				await ctx.db.insert("usageTracking", {
					action: "api_call",
					dayWindow: today,
					hourWindow: now.toISOString().slice(0, 13),
					monthWindow: now.toISOString().slice(0, 7),
					count: 1,
					tokenCost: 1, // 1 cent each
					createdAt: now.getTime() + i,
					updatedAt: now.getTime() + i,
				});
			});
		}

		const limits = await t.run((ctx) => checkUsageLimits(ctx as any, {}));

		expect(limits.withinLimits).toBe(true); // Should still be within limits
		expect(limits.warnings.length).toBeGreaterThan(0); // But should have warnings
		expect(limits.warnings[0]).toContain("Approaching");
		expect(limits.stats.requestCount).toBe(85);
	});

	test("should detect limit exceeded", async () => {
		const t = convexTest(schema, modules);

		const now = new Date();
		const today = now.toISOString().slice(0, 10);

		// Insert usage data exceeding limits
		for (let i = 0; i < 110; i++) {
			await t.run(async (ctx) => {
				await ctx.db.insert("usageTracking", {
					action: "api_call",
					dayWindow: today,
					hourWindow: now.toISOString().slice(0, 13),
					monthWindow: now.toISOString().slice(0, 7),
					count: 1,
					tokenCost: 1,
					createdAt: now.getTime() + i,
					updatedAt: now.getTime() + i,
				});
			});
		}

		const limits = await t.run((ctx) => checkUsageLimits(ctx as any, {}));

		expect(limits.withinLimits).toBe(false); // Should exceed limits
		expect(limits.warnings.length).toBeGreaterThan(0);
		expect(limits.warnings.some((w) => w.includes("exceeded"))).toBe(true);
		expect(limits.stats.requestCount).toBe(110);
	});

	test("should handle cost limit checks", async () => {
		const t = convexTest(schema, modules);

		const now = new Date();
		const today = now.toISOString().slice(0, 10);

		// Insert high-cost usage data
		await t.run(async (ctx) => {
			await ctx.db.insert("usageTracking", {
				action: "api_call",
				dayWindow: today,
				hourWindow: now.toISOString().slice(0, 13),
				monthWindow: now.toISOString().slice(0, 7),
				count: 1,
				tokenCost: 600, // $6.00 (exceeds $5 limit)
				createdAt: now.getTime(),
				updatedAt: now.getTime(),
			});
		});

		const limits = await t.run((ctx) => checkUsageLimits(ctx as any, {}));

		expect(limits.withinLimits).toBe(false);
		expect(limits.warnings.some((w) => w.includes("cost limit"))).toBe(true);
		expect(limits.stats.totalCost).toBe(6.0);
	});

	test("should handle session-based tracking", async () => {
		const t = convexTest(schema, modules);

		const sessionId = "test-session-123";
		const now = new Date();
		const today = now.toISOString().slice(0, 10);

		await t.run(async (ctx) => {
			await ctx.db.insert("usageTracking", {
				sessionId,
				action: "api_call",
				dayWindow: today,
				hourWindow: now.toISOString().slice(0, 13),
				monthWindow: now.toISOString().slice(0, 7),
				count: 1,
				tokenCost: 25,
				createdAt: now.getTime(),
				updatedAt: now.getTime(),
			});
		});

		const stats = await t.run((ctx) =>
			getUsageStats(ctx as any, { sessionId }, "day"),
		);

		expect(stats.requestCount).toBe(1);
		expect(stats.totalCost).toBe(0.25);
	});

	test("should handle different time windows", async () => {
		const t = convexTest(schema, modules);

		const now = new Date();

		// Insert data for different time windows
		await t.run(async (ctx) => {
			await ctx.db.insert("usageTracking", {
				action: "api_call",
				dayWindow: now.toISOString().slice(0, 10),
				hourWindow: now.toISOString().slice(0, 13),
				monthWindow: now.toISOString().slice(0, 7),
				count: 1,
				tokenCost: 10,
				createdAt: now.getTime(),
				updatedAt: now.getTime(),
			});
		});

		const hourStats = await t.run((ctx) =>
			getUsageStats(ctx as any, {}, "hour"),
		);
		const dayStats = await t.run((ctx) => getUsageStats(ctx as any, {}, "day"));
		const monthStats = await t.run((ctx) =>
			getUsageStats(ctx as any, {}, "month"),
		);

		// All should show the same record since it's current
		expect(hourStats.requestCount).toBe(1);
		expect(dayStats.requestCount).toBe(1);
		expect(monthStats.requestCount).toBe(1);
	});
});
