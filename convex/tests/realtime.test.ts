import { expect, test, describe } from "vitest";
import { convexTest } from "convex-test";
import { api } from "../_generated/api";
import schema from "../schema";

// Import all Convex function modules for testing
const modules = import.meta.glob("../**/!(*.*.*)*.*s");

describe("Real-time and Persistence", () => {
	test("messages should be persisted and delivered via real-time subscription", async () => {
		const t = convexTest(schema, modules);

		const sessionId = await t.mutation(api.functions.prd.createSession, {
			userId: "test-user-realtime",
			projectName: "Real-time Test",
		});

		await t.mutation(api.functions.agents.initializeGeneral, { sessionId });

		const firstResult = await t.query(api.functions.agents.getConversation, {
			sessionId,
		});

		expect(firstResult.messages).toHaveLength(1); // Initial greeting

		await t.mutation(api.functions.agents.sendMessage, {
			sessionId,
			senderId: "test-user-realtime",
			content: "1",
		});

		const secondResult = await t.query(api.functions.agents.getConversation, {
			sessionId,
		});

		expect(secondResult.messages.length).toBeGreaterThan(1);
		const lastMessage = secondResult.messages[secondResult.messages.length - 1];
		expect(lastMessage.senderType).toBe("agent");
	});
});
