/**
 * Rate Limiting and Cost Optimization
 *
 * Implements rate limiting for AI requests and cost tracking
 * to prevent abuse and manage operational costs.
 */

/**
 * Rate limiter configuration
 */
export interface RateLimitConfig {
	name: string;
	kind: "token bucket" | "fixed window";
	rate: { count: number; period: number }; // count per period (ms)
	capacity?: number; // for token bucket
	shards?: number; // for performance
}

/**
 * Rate limit configurations for different operations
 */
export const RATE_LIMIT_CONFIGS: Record<string, RateLimitConfig> = {
	// AI message generation - most expensive operation
	ai_message: {
		name: "ai_message_generation",
		kind: "token bucket",
		rate: { count: 10, period: 60 * 60 * 1000 }, // 10 per hour
		capacity: 15, // Allow bursts up to 15
		shards: 10,
	},

	// PRD session creation - moderate cost
	session_creation: {
		name: "prd_session_creation",
		kind: "token bucket",
		rate: { count: 5, period: 60 * 60 * 1000 }, // 5 per hour
		capacity: 8,
		shards: 5,
	},

	// Message sending - lower cost but high frequency
	message_send: {
		name: "message_sending",
		kind: "fixed window",
		rate: { count: 50, period: 60 * 60 * 1000 }, // 50 per hour
		shards: 10,
	},

	// General API calls - very permissive
	api_call: {
		name: "general_api_calls",
		kind: "fixed window",
		rate: { count: 200, period: 60 * 60 * 1000 }, // 200 per hour
		shards: 10,
	},
};

/**
 * Create rate limiter instance
 */
export function createRateLimiter(_config: RateLimitConfig): null {
	// TODO: Fix RateLimiter configuration - API may have changed
	void _config;
	return null;
}

/**
 * Rate limiter instances
 */
export const rateLimiters = {
	aiMessage: null as null,
	sessionCreation: null as null,
	messageSend: null as null,
	apiCall: null as null,
};

/**
 * Check rate limit for a user/session
 */
export async function checkRateLimit(
	ctx: { db?: unknown },
	limiterName: keyof typeof rateLimiters,
	identifier: string,
	_throwOnLimit = true,
): Promise<{ allowed: boolean; retryAfter?: number }> {
	// TODO: Implement actual rate limiting when API is fixed
	// Suppress unused parameters
	void ctx;
	void limiterName;
	void identifier;
	void _throwOnLimit;
	return { allowed: true };
}

/**
 * Cost tracking interface
 */
export interface CostMetrics {
	operation: string;
	provider: "anthropic" | "openai";
	model: string;
	tokensUsed: {
		prompt: number;
		completion: number;
		total: number;
	};
	cost: number; // USD
	responseTime: number; // ms
	timestamp: number;
	userId?: string;
	sessionId?: string;
}

/**
 * Track operation cost and usage
 */
export async function trackCost(
	ctx: {
		db: {
			insert: (table: string, data: unknown) => Promise<unknown>;
			query: (table: string) => unknown;
			patch: (id: string, data: unknown) => Promise<unknown>;
		};
	},
	metrics: CostMetrics,
): Promise<void> {
	const now = Date.now();
	const hourWindow = new Date(now).toISOString().slice(0, 13); // YYYY-MM-DDTHH
	const dayWindow = new Date(now).toISOString().slice(0, 10); // YYYY-MM-DD
	const monthWindow = new Date(now).toISOString().slice(0, 7); // YYYY-MM

	// Track in usage table
	await ctx.db.insert("usageTracking", {
		userId: metrics.userId,
		sessionId: metrics.sessionId,
		action: "api_call",
		hourWindow,
		dayWindow,
		monthWindow,
		count: 1,
		tokenCost: Math.round(metrics.cost * 100), // Store as cents
		createdAt: now,
		updatedAt: now,
	});

	// Update system metrics (optional - for monitoring)
	await updateSystemMetrics(ctx, metrics);
}

/**
 * Update system-wide cost metrics
 */
async function updateSystemMetrics(
	ctx: {
		db: {
			query: (table: string) => unknown;
			patch: (id: string, data: unknown) => Promise<unknown>;
			insert: (table: string, data: unknown) => Promise<unknown>;
		};
	},
	metrics: CostMetrics,
): Promise<void> {
	const today = new Date().toISOString().slice(0, 10);
	const configKey = `daily_costs_${today}`;

	try {
		// Get existing daily costs
		const existingConfig = await (
			ctx.db.query("systemConfig") as {
				withIndex: (
					index: string,
					fn: (q: {
						eq: (
							field: string,
							value: string,
						) => { eq: (field: string, value: string) => unknown };
					}) => unknown,
				) => {
					first: () => Promise<{ _id: string; value: string } | null>;
				};
			}
		)
			.withIndex(
				"by_key",
				(q: {
					eq: (
						field: string,
						value: string,
					) => { eq: (field: string, value: string) => unknown };
				}) => q.eq("key", configKey),
			)
			.first();

		let dailyCosts = { totalCost: 0, totalTokens: 0, requestCount: 0 };

		if (existingConfig) {
			dailyCosts = JSON.parse(existingConfig.value);
		}

		// Update metrics
		dailyCosts.totalCost += metrics.cost;
		dailyCosts.totalTokens += metrics.tokensUsed.total;
		dailyCosts.requestCount += 1;

		// Store/update
		if (existingConfig) {
			await ctx.db.patch(existingConfig._id, {
				value: JSON.stringify(dailyCosts),
				updatedAt: Date.now(),
			});
		} else {
			await ctx.db.insert("systemConfig", {
				key: configKey,
				value: JSON.stringify(dailyCosts),
				description: `Daily AI costs and usage for ${today}`,
				category: "cost_tracking",
				createdAt: Date.now(),
				updatedAt: Date.now(),
				version: 1,
			});
		}
	} catch (error) {
		console.warn("Failed to update system metrics:", error);
		// Don't throw - metrics failure shouldn't break the main operation
	}
}

/**
 * Get usage statistics for a user/session
 */
export async function getUsageStats(
	ctx: { db: { query: (table: string) => unknown } },
	identifier: { userId?: string; sessionId?: string },
	timeWindow: "hour" | "day" | "month" = "day",
): Promise<{
	requestCount: number;
	totalCost: number;
	averageCost: number;
	tokenUsage: number;
}> {
	const now = new Date();
	let windowValue: string;

	switch (timeWindow) {
		case "hour":
			windowValue = now.toISOString().slice(0, 13);
			break;
		case "day":
			windowValue = now.toISOString().slice(0, 10);
			break;
		case "month":
			windowValue = now.toISOString().slice(0, 7);
			break;
	}

	let query = ctx.db.query("usageTracking") as {
		withIndex: (
			index: string,
			fn: (q: {
				eq: (
					field: string,
					value: string,
				) => { eq: (field: string, value: string) => unknown };
			}) => unknown,
		) => {
			collect: () => Promise<{ count: number; tokenCost?: number }[]>;
		};
		collect: () => Promise<{ count: number; tokenCost?: number }[]>;
	};

	if (identifier.userId) {
		query = query.withIndex(
			"by_user_day",
			(q: {
				eq: (
					field: string,
					value: string,
				) => { eq: (field: string, value: string) => unknown };
			}) => q.eq("userId", identifier.userId!).eq("dayWindow", windowValue),
		) as typeof query;
	} else if (identifier.sessionId) {
		query = query.withIndex(
			"by_session_day",
			(q: {
				eq: (
					field: string,
					value: string,
				) => { eq: (field: string, value: string) => unknown };
			}) =>
				q.eq("sessionId", identifier.sessionId!).eq("dayWindow", windowValue),
		) as typeof query;
	}

	const usage = await query.collect();

	const stats = usage.reduce(
		(
			acc: { requestCount: number; totalCost: number; tokenUsage: number },
			record: { count: number; tokenCost?: number },
		) => ({
			requestCount: acc.requestCount + record.count,
			totalCost: acc.totalCost + (record.tokenCost || 0) / 100, // Convert from cents
			tokenUsage: acc.tokenUsage + record.count * 1000, // Rough estimate
		}),
		{ requestCount: 0, totalCost: 0, tokenUsage: 0 },
	);

	return {
		...stats,
		averageCost:
			stats.requestCount > 0 ? stats.totalCost / stats.requestCount : 0,
	};
}

/**
 * Check if user/session is approaching limits
 */
export async function checkUsageLimits(
	ctx: { db: { query: (table: string) => unknown } },
	identifier: { userId?: string; sessionId?: string },
): Promise<{
	withinLimits: boolean;
	warnings: string[];
	stats: {
		requestCount: number;
		totalCost: number;
		averageCost: number;
		tokenUsage: number;
	};
}> {
	const stats = await getUsageStats(ctx, identifier, "day");
	const warnings: string[] = [];

	// Define limits (can be made configurable)
	const DAILY_LIMITS = {
		requests: 100,
		cost: 5.0, // $5 USD
		tokens: 100000,
	};

	let withinLimits = true;

	if (stats.requestCount >= DAILY_LIMITS.requests * 0.8) {
		warnings.push(
			`Approaching daily request limit (${stats.requestCount}/${DAILY_LIMITS.requests})`,
		);
	}

	if (stats.requestCount >= DAILY_LIMITS.requests) {
		withinLimits = false;
		warnings.push("Daily request limit exceeded");
	}

	if (stats.totalCost >= DAILY_LIMITS.cost * 0.8) {
		warnings.push(
			`Approaching daily cost limit ($${stats.totalCost.toFixed(2)}/$${DAILY_LIMITS.cost})`,
		);
	}

	if (stats.totalCost >= DAILY_LIMITS.cost) {
		withinLimits = false;
		warnings.push("Daily cost limit exceeded");
	}

	return {
		withinLimits,
		warnings,
		stats,
	};
}

/**
 * Get rate limit status for a user
 */
export async function getRateLimitStatus(
	_identifier: string,
): Promise<Record<string, { allowed: boolean; retryAfter?: number }>> {
	// TODO: Implement actual rate limiting when API is fixed
	void _identifier;
	return {
		aiMessage: { allowed: true },
		sessionCreation: { allowed: true },
		messageSend: { allowed: true },
		apiCall: { allowed: true },
	};
}
