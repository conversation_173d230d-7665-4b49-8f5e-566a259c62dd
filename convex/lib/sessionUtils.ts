import { ConvexError } from "convex/values";
import { generateSecureSessionId } from "../../src/lib/sessionUtils";
import type { MutationCtx } from "../_generated/server";

// Re-export for convenience
export { generateSecureSessionId };

/**
 * Ensure session ID uniqueness by checking database
 *
 * This function takes a proposed session ID (or generates one if not provided)
 * and ensures it's unique in the conversations table. If a collision is found,
 * it will generate new IDs and retry up to maxAttempts times.
 *
 * @param ctx - Convex mutation context for database access
 * @param proposedSessionId - Optional session ID to check for uniqueness
 * @returns Promise<string> - A guaranteed unique session ID
 * @throws ConvexError if unable to generate unique ID after max attempts
 */
export async function ensureUniqueSessionId(
	ctx: MutationCtx,
	proposedSessionId?: string,
): Promise<string> {
	let sessionId = proposedSessionId || generateSecureSessionId();
	let attempts = 0;
	const maxAttempts = 5;

	while (attempts < maxAttempts) {
		// Check if session ID already exists in conversations table
		const existing = await ctx.db
			.query("conversations")
			.withIndex("by_session", (q) => q.eq("sessionId", sessionId))
			.first();

		if (!existing) {
			return sessionId;
		}

		// Generate new ID and increment attempts (except for first proposed ID collision)
		const isFirstProposedIdCollision = attempts === 0 && proposedSessionId;
		sessionId = generateSecureSessionId();
		if (!isFirstProposedIdCollision) {
			attempts++;
		}
	}

	throw new ConvexError(
		"Failed to generate unique session ID after multiple attempts",
	);
}

// Re-export the validation function from shared utilities
export { isValidSessionIdFormat } from "../../src/lib/sessionUtils";
