/**
 * PRD Questionnaire Workflow Engine
 *
 * Implements menu-driven questionnaire workflow for TheGeneral agent
 * with state machine step progression and context preservation.
 */

export interface WorkflowStep {
	id: string;
	name: string;
	description: string;
	questions: WorkflowQuestion[];
	prerequisites: string[];
	nextSteps: string[];
	isRequired: boolean;
}

export interface WorkflowQuestion {
	id: string;
	text: string;
	type: "menu" | "text" | "confirmation";
	options?: MenuOption[];
	validation?: {
		required: boolean;
		minLength?: number;
		pattern?: string;
	};
}

export interface MenuOption {
	value: string;
	label: string;
	description?: string;
	nextStep?: string;
}

export interface WorkflowState {
	currentStep: string;
	completedSteps: string[];
	responses: Record<string, unknown>;
	context: Record<string, unknown>;
	progress: {
		totalSteps: number;
		completedCount: number;
		percentComplete: number;
	};
}

/**
 * PRD Questionnaire Workflow Definition
 */
export const PRD_QUESTIONNAIRE_WORKFLOW: WorkflowStep[] = [
	{
		id: "initialization",
		name: "Project Initialization",
		description: "Welcome and basic project setup",
		prerequisites: [],
		nextSteps: ["target_audience"],
		isRequired: true,
		questions: [
			{
				id: "welcome_choice",
				text: "READY TO BEGIN THE DRILL?",
				type: "menu",
				options: [
					{
						value: "start",
						label: "Yes sir! Let's create this PRD!",
						nextStep: "target_audience",
					},
					{
						value: "process",
						label: "I need to understand the process first",
						nextStep: "process_explanation",
					},
					{
						value: "customize",
						label: "Can we customize the approach?",
						nextStep: "customization",
					},
				],
				validation: { required: true },
			},
		],
	},
	{
		id: "process_explanation",
		name: "Process Overview",
		description: "Explain the PRD creation process",
		prerequisites: ["initialization"],
		nextSteps: ["target_audience", "customization"],
		isRequired: false,
		questions: [
			{
				id: "after_explanation",
				text: "Ready to begin Phase 1?",
				type: "menu",
				options: [
					{
						value: "begin",
						label: "Yes, let's start with target audience",
						nextStep: "target_audience",
					},
					{
						value: "sample",
						label: "I want to see a sample PRD first",
						nextStep: "sample_prd",
					},
					{
						value: "modify",
						label: "Can we modify this process?",
						nextStep: "customization",
					},
				],
				validation: { required: true },
			},
		],
	},
	{
		id: "customization",
		name: "Workflow Customization",
		description: "Customize the PRD creation approach",
		prerequisites: ["initialization"],
		nextSteps: ["target_audience"],
		isRequired: false,
		questions: [
			{
				id: "customization_choice",
				text: "WHAT'S YOUR TACTICAL PREFERENCE?",
				type: "menu",
				options: [
					{
						value: "skip_audience",
						label: "Skip audience deep-dive (for internal projects)",
						nextStep: "problem_statement",
					},
					{
						value: "extended_research",
						label: "Extended market research phase",
						nextStep: "market_research",
					},
					{
						value: "competitive_focus",
						label: "Competitive analysis emphasis",
						nextStep: "competitive_analysis",
					},
					{
						value: "rapid_prototype",
						label: "Rapid prototype PRD (minimal viable)",
						nextStep: "rapid_mode",
					},
					{
						value: "comprehensive",
						label: "Comprehensive enterprise PRD",
						nextStep: "comprehensive_mode",
					},
					{
						value: "technical_focus",
						label: "Technical specification focus",
						nextStep: "technical_mode",
					},
					{
						value: "standard",
						label: "Standard process works fine",
						nextStep: "target_audience",
					},
				],
				validation: { required: true },
			},
		],
	},
	{
		id: "target_audience",
		name: "Target Audience Analysis",
		description: "Define and analyze target users",
		prerequisites: ["initialization"],
		nextSteps: ["problem_statement"],
		isRequired: true,
		questions: [
			{
				id: "audience_type",
				text: "WHO exactly will use your product?",
				type: "menu",
				options: [
					{
						value: "b2b",
						label: "B2B professionals in [specific industry]",
						nextStep: "b2b_details",
					},
					{
						value: "b2c",
						label: "B2C consumers with [specific need]",
						nextStep: "b2c_details",
					},
					{
						value: "internal",
						label: "Internal teams/employees",
						nextStep: "internal_details",
					},
					{
						value: "technical",
						label: "Technical users (developers, admins)",
						nextStep: "technical_details",
					},
					{
						value: "mixed",
						label: "Mixed audience (explain the segments)",
						nextStep: "mixed_details",
					},
				],
				validation: { required: true },
			},
		],
	},
	{
		id: "problem_statement",
		name: "Problem Definition",
		description: "Define the core problem being solved",
		prerequisites: ["target_audience"],
		nextSteps: ["solution_overview"],
		isRequired: true,
		questions: [
			{
				id: "problem_description",
				text: "What SPECIFIC problem does your product solve?",
				type: "text",
				validation: { required: true, minLength: 50 },
			},
			{
				id: "problem_validation",
				text: "How do you KNOW this is a real problem?",
				type: "menu",
				options: [
					{ value: "user_research", label: "User research and interviews" },
					{ value: "market_data", label: "Market research and data" },
					{ value: "personal_experience", label: "Personal/team experience" },
					{ value: "customer_feedback", label: "Existing customer feedback" },
					{ value: "competitor_gaps", label: "Competitor analysis gaps" },
				],
				validation: { required: true },
			},
		],
	},
	{
		id: "solution_overview",
		name: "Solution Overview",
		description: "High-level solution approach",
		prerequisites: ["problem_statement"],
		nextSteps: ["core_features"],
		isRequired: true,
		questions: [
			{
				id: "solution_approach",
				text: "HOW will your product solve this problem?",
				type: "text",
				validation: { required: true, minLength: 100 },
			},
			{
				id: "unique_value",
				text: "What makes your solution DIFFERENT from existing alternatives?",
				type: "text",
				validation: { required: true, minLength: 50 },
			},
		],
	},
	{
		id: "core_features",
		name: "Core Features Definition",
		description: "Define essential product features",
		prerequisites: ["solution_overview"],
		nextSteps: ["success_metrics"],
		isRequired: true,
		questions: [
			{
				id: "feature_priority",
				text: "What are the TOP 3 MUST-HAVE features?",
				type: "text",
				validation: { required: true, minLength: 100 },
			},
			{
				id: "feature_scope",
				text: "What features are OUT OF SCOPE for the first version?",
				type: "text",
				validation: { required: true, minLength: 50 },
			},
		],
	},
	{
		id: "success_metrics",
		name: "Success Metrics",
		description: "Define how success will be measured",
		prerequisites: ["core_features"],
		nextSteps: ["technical_requirements"],
		isRequired: true,
		questions: [
			{
				id: "primary_metrics",
				text: "What metrics will determine if this product is SUCCESSFUL?",
				type: "text",
				validation: { required: true, minLength: 50 },
			},
			{
				id: "success_timeline",
				text: "When do you expect to see these results?",
				type: "menu",
				options: [
					{ value: "immediate", label: "Within 1 month of launch" },
					{ value: "short_term", label: "3-6 months after launch" },
					{ value: "medium_term", label: "6-12 months after launch" },
					{ value: "long_term", label: "12+ months after launch" },
				],
				validation: { required: true },
			},
		],
	},
	{
		id: "technical_requirements",
		name: "Technical Requirements",
		description: "Technical constraints and requirements",
		prerequisites: ["success_metrics"],
		nextSteps: ["completion"],
		isRequired: true,
		questions: [
			{
				id: "platform_requirements",
				text: "What platforms/technologies are REQUIRED?",
				type: "menu",
				options: [
					{ value: "web_only", label: "Web application only" },
					{ value: "mobile_first", label: "Mobile-first (iOS/Android)" },
					{ value: "desktop", label: "Desktop application" },
					{ value: "cross_platform", label: "Cross-platform (web + mobile)" },
					{ value: "api_service", label: "API/Service backend" },
				],
				validation: { required: true },
			},
			{
				id: "technical_constraints",
				text: "Any technical CONSTRAINTS or requirements?",
				type: "text",
				validation: { required: false },
			},
			{
				id: "integration_needs",
				text: "What systems need to INTEGRATE with your product?",
				type: "text",
				validation: { required: false },
			},
		],
	},
	{
		id: "completion",
		name: "PRD Generation",
		description: "Generate final PRD document",
		prerequisites: ["technical_requirements"],
		nextSteps: [],
		isRequired: true,
		questions: [
			{
				id: "final_review",
				text: "Ready to generate your COMPREHENSIVE PRD?",
				type: "confirmation",
				validation: { required: true },
			},
		],
	},
];

/**
 * Workflow State Machine
 */
export class PRDWorkflowEngine {
	private workflow: WorkflowStep[];
	private state: WorkflowState;

	constructor(initialState?: Partial<WorkflowState>) {
		this.workflow = PRD_QUESTIONNAIRE_WORKFLOW;
		this.state = {
			currentStep: "initialization",
			completedSteps: [],
			responses: {},
			context: {},
			progress: {
				totalSteps: this.workflow.filter((step) => step.isRequired).length,
				completedCount: 0,
				percentComplete: 0,
			},
			...initialState,
		};
	}

	/**
	 * Get current workflow step
	 */
	getCurrentStep(): WorkflowStep | null {
		return (
			this.workflow.find((step) => step.id === this.state.currentStep) || null
		);
	}

	/**
	 * Process user response and advance workflow
	 */
	processResponse(
		questionId: string,
		response: unknown,
	): {
		success: boolean;
		nextStep?: string;
		validationErrors?: string[];
		message?: string;
	} {
		const currentStep = this.getCurrentStep();
		if (!currentStep) {
			return { success: false, message: "Invalid workflow state" };
		}

		const question = currentStep.questions.find((q) => q.id === questionId);
		if (!question) {
			return { success: false, message: "Question not found" };
		}

		// Validate response
		const validation = this.validateResponse(question, response);
		if (!validation.isValid) {
			return { success: false, validationErrors: validation.errors };
		}

		// Store response
		this.state.responses[questionId] = response;

		// Determine next step
		let nextStep: string | undefined;

		if (question.type === "menu" && question.options) {
			const selectedOption = question.options.find(
				(opt) => opt.value === response,
			);
			nextStep = selectedOption?.nextStep;
		}

		// If no specific next step, use default from current step
		if (!nextStep && currentStep.nextSteps.length > 0) {
			nextStep = currentStep.nextSteps[0];
		}

		// Mark current step as completed if all questions answered
		const allQuestionsAnswered = currentStep.questions.every(
			(q) => this.state.responses[q.id] !== undefined,
		);

		if (
			allQuestionsAnswered &&
			!this.state.completedSteps.includes(currentStep.id)
		) {
			this.state.completedSteps.push(currentStep.id);
			this.updateProgress();
		}

		// Move to next step
		if (nextStep) {
			this.state.currentStep = nextStep;
		}

		return { success: true, nextStep };
	}

	/**
	 * Validate user response
	 */
	private validateResponse(
		question: WorkflowQuestion,
		response: unknown,
	): {
		isValid: boolean;
		errors: string[];
	} {
		const errors: string[] = [];

		if (
			question.validation?.required &&
			(!response || response.toString().trim() === "")
		) {
			errors.push("This field is required");
		}

		if (
			question.validation?.minLength &&
			response &&
			response.toString().length < question.validation.minLength
		) {
			errors.push(
				`Response must be at least ${question.validation.minLength} characters`,
			);
		}

		if (question.validation?.pattern && response) {
			const regex = new RegExp(question.validation.pattern);
			if (!regex.test(response.toString())) {
				errors.push("Response format is invalid");
			}
		}

		if (question.type === "menu" && question.options) {
			const validValues = question.options.map((opt) => opt.value);
			if (typeof response === "string" && !validValues.includes(response)) {
				errors.push("Please select a valid option");
			} else if (typeof response !== "string") {
				errors.push("Please select a valid option");
			}
		}

		return {
			isValid: errors.length === 0,
			errors,
		};
	}

	/**
	 * Update progress tracking
	 */
	private updateProgress(): void {
		const requiredSteps = this.workflow.filter((step) => step.isRequired);
		const completedRequiredSteps = requiredSteps.filter((step) =>
			this.state.completedSteps.includes(step.id),
		);

		this.state.progress = {
			totalSteps: requiredSteps.length,
			completedCount: completedRequiredSteps.length,
			percentComplete: Math.round(
				(completedRequiredSteps.length / requiredSteps.length) * 100,
			),
		};
	}

	/**
	 * Get workflow state
	 */
	getState(): WorkflowState {
		return { ...this.state };
	}

	/**
	 * Check if workflow is complete
	 */
	isComplete(): boolean {
		return this.state.progress.percentComplete === 100;
	}

	/**
	 * Get next question to ask
	 */
	getNextQuestion(): WorkflowQuestion | null {
		const currentStep = this.getCurrentStep();
		if (!currentStep) return null;

		// Find first unanswered question in current step
		return (
			currentStep.questions.find(
				(q) => this.state.responses[q.id] === undefined,
			) || null
		);
	}

	/**
	 * Generate PRD content from collected responses
	 */
	generatePRD(): string {
		const responses = this.state.responses;

		return `# Product Requirements Document

## Project Overview
**Project Name:** ${this.state.context.projectName || "Untitled Project"}

## Target Audience
${responses.audience_type || "To be defined"}

## Problem Statement
${responses.problem_description || "To be defined"}

**Problem Validation:** ${responses.problem_validation || "To be defined"}

## Solution Overview
${responses.solution_approach || "To be defined"}

**Unique Value Proposition:** ${responses.unique_value || "To be defined"}

## Core Features
**Must-Have Features:**
${responses.feature_priority || "To be defined"}

**Out of Scope:**
${responses.feature_scope || "To be defined"}

## Success Metrics
${responses.primary_metrics || "To be defined"}

**Timeline:** ${responses.success_timeline || "To be defined"}

## Technical Requirements
**Platform Requirements:** ${responses.platform_requirements || "To be defined"}

**Technical Constraints:** ${responses.technical_constraints || "None specified"}

**Integration Needs:** ${responses.integration_needs || "None specified"}

---
*Generated by TheGeneral PRD Creation System*
*Date: ${new Date().toLocaleDateString()}*`;
	}
}
