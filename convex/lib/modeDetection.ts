/**
 * Shared mode detection functions and interfaces for PRD generation
 */

/**
 * Message interface for mode detection
 */
export interface Message {
	id?: string;
	role: "user" | "assistant" | "system";
	content: string;
}

/**
 * Basic message structure for mode detection functions
 */
export interface BasicMessage {
	role: string;
	content: string;
}

/**
 * Determines if the user wants to generate a PRD based on their message content
 */
export function shouldGeneratePRD(messages: BasicMessage[]): boolean {
	if (!messages || messages.length === 0) return false;

	const lastUserMessage = messages.filter((msg) => msg.role === "user").pop();

	if (!lastUserMessage) return false;

	const content = lastUserMessage.content.toLowerCase();
	const prdTriggers = [
		"generate prd",
		"create prd",
		"write prd",
		"generate the prd",
		"create the prd",
		"write the prd",
		"make prd",
		"build prd",
		"let's generate",
		"ready for prd",
		"create my prd",
		"i'm ready",
		"generate it",
		"make the prd",
		"ready to generate",
		"let's create",
		"build my prd",
	];

	return prdTriggers.some((trigger) => content.includes(trigger));
}

/**
 * Determines if the user wants to refine an existing PRD
 */
export function shouldRefinePRD(messages: BasicMessage[]): boolean {
	if (!messages || messages.length === 0) return false;

	const lastUserMessage = messages.filter((msg) => msg.role === "user").pop();

	if (!lastUserMessage) return false;

	const content = lastUserMessage.content.toLowerCase();
	const refinementTriggers = [
		"refine the prd",
		"improve the prd",
		"update the prd",
		"change the prd",
		"modify the prd",
		"revise the prd",
		"edit the prd",
		"fix the prd",
		"adjust the prd",
		"refine",
		"improve",
		"modify",
		"revise",
		"update",
		"change",
		"edit",
		"adjust",
	];

	return refinementTriggers.some((trigger) => content.includes(trigger));
}

/**
 * Determines if the clarification phase is complete based on conversation depth and indicators
 */
export function isClarificationComplete(messages: BasicMessage[]): boolean {
	if (!messages || messages.length < 8) return false;

	const userMessages = messages.filter((msg) => msg.role === "user");
	const assistantMessages = messages.filter((msg) => msg.role === "assistant");

	const conversationText = messages
		.map((msg) => msg.content.toLowerCase())
		.join(" ");

	const completionIndicators = [
		"that's clear",
		"yes that's right",
		"exactly",
		"perfect",
		"that's correct",
		"sounds good",
		"that works",
		"i agree",
		"that's it",
		"that's the one",
	];

	const hasCompletionIndicators = completionIndicators.some((indicator) =>
		conversationText.includes(indicator),
	);

	const clarificationTopics = [
		"problem",
		"issue",
		"challenge",
		"feature",
		"solution",
		"functionality",
		"user",
		"customer",
		"audience",
		"target",
		"metric",
		"measure",
		"success",
		"kpi",
	];

	const topicsCovered = clarificationTopics.filter((topic) =>
		conversationText.includes(topic),
	).length;

	return (
		userMessages.length >= 3 &&
		assistantMessages.length >= 3 &&
		topicsCovered >= 6 &&
		hasCompletionIndicators
	);
}

/**
 * Detects the current conversation mode based on message content and conversation state
 */
export function detectMode(
	messages: BasicMessage[],
): "clarification" | "generation" | "refinement" {
	if (shouldRefinePRD(messages)) {
		return "refinement";
	}

	if (shouldGeneratePRD(messages) && isClarificationComplete(messages)) {
		return "generation";
	}

	return "clarification";
}
