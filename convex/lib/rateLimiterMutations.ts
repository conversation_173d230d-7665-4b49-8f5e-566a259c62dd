import { MINUTE, RateLimiter } from "@convex-dev/rate-limiter";
import { ConvexError, v } from "convex/values";
import { components } from "../_generated/api";
import { mutation } from "../_generated/server";

const rateLimiter = new RateLimiter(components.rateLimiter, {
	chatCompletion: {
		kind: "token bucket",
		rate: 5,
		period: MINUTE,
		capacity: 10,
	},
});

/**
 * Chat rate limiting mutation that can be called from actions
 */
export const checkChatRateLimit = mutation({
	args: {
		identifier: v.string(),
	},
	handler: async (ctx, args) => {
		const key = `chat:${args.identifier}`;

		try {
			await rateLimiter.limit(ctx, "chatCompletion", { key });
		} catch {
			throw new ConvexError({
				code: "RATE_LIMITED",
				message:
					"Chat rate limit exceeded. Please wait before sending another message.",
				status: 429,
			});
		}
	},
});
