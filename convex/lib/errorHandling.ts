import { ConvexError } from "convex/values";

/**
 * Comprehensive Error Handling and Recovery
 *
 * Provides robust error handling, retry logic, failover mechanisms,
 * and state recovery for TheGeneral agent system.
 */

export interface RetryConfig {
	maxAttempts: number;
	baseDelay: number; // milliseconds
	maxDelay: number; // milliseconds
	backoffMultiplier: number;
	retryableErrors: string[];
}

export interface ErrorContext {
	operation: string;
	userId?: string;
	sessionId?: string;
	agentId?: string;
	timestamp: number;
	metadata?: Record<string, unknown>;
}

/**
 * Default retry configurations for different operations
 */
export const RETRY_CONFIGS: Record<string, RetryConfig> = {
	ai_request: {
		maxAttempts: 3,
		baseDelay: 1000,
		maxDelay: 10000,
		backoffMultiplier: 2,
		retryableErrors: [
			"NETWORK_ERROR",
			"TIMEOUT_ERROR",
			"RATE_LIMITED",
			"TEMPORARY_FAILURE",
			"SERVICE_UNAVAILABLE",
		],
	},
	database_operation: {
		maxAttempts: 5,
		baseDelay: 500,
		maxDelay: 5000,
		backoffMultiplier: 1.5,
		retryableErrors: ["NETWORK_ERROR", "TIMEOUT_ERROR", "TEMPORARY_FAILURE"],
	},
	agent_initialization: {
		maxAttempts: 2,
		baseDelay: 2000,
		maxDelay: 8000,
		backoffMultiplier: 2,
		retryableErrors: [
			"NETWORK_ERROR",
			"TEMPORARY_FAILURE",
			"SERVICE_UNAVAILABLE",
		],
	},
};

/**
 * Custom error types for the system
 */
export class SystemError extends Error {
	public readonly code: string;
	public readonly context: ErrorContext;
	public readonly retryable: boolean;
	public readonly severity: "low" | "medium" | "high" | "critical";

	constructor(
		code: string,
		message: string,
		context: ErrorContext,
		options: {
			retryable?: boolean;
			severity?: "low" | "medium" | "high" | "critical";
			cause?: Error;
		} = {},
	) {
		super(message);
		this.name = "SystemError";
		this.code = code;
		this.context = context;
		this.retryable = options.retryable ?? false;
		this.severity = options.severity ?? "medium";

		if (options.cause) {
			// this.cause = options.cause;
		}
	}
}

/**
 * AI Provider Error - specific to AI service failures
 */
export class AIProviderError extends SystemError {
	public readonly provider: "anthropic" | "openai";
	public readonly model: string;

	constructor(
		provider: "anthropic" | "openai",
		model: string,
		message: string,
		context: ErrorContext,
		options: { retryable?: boolean; cause?: Error } = {},
	) {
		super("AI_PROVIDER_ERROR", message, context, {
			...options,
			severity: "high",
		});
		this.provider = provider;
		this.model = model;
	}
}

/**
 * Agent State Error - specific to agent workflow failures
 */
export class AgentStateError extends SystemError {
	public readonly agentType: string;
	public readonly workflowStep: string;

	constructor(
		agentType: string,
		workflowStep: string,
		message: string,
		context: ErrorContext,
		options: { retryable?: boolean; cause?: Error } = {},
	) {
		super("AGENT_STATE_ERROR", message, context, {
			...options,
			severity: "high",
		});
		this.agentType = agentType;
		this.workflowStep = workflowStep;
	}
}

/**
 * Retry operation with exponential backoff
 */
export async function retryWithBackoff<T>(
	operation: () => Promise<T>,
	config: RetryConfig,
	context: ErrorContext,
): Promise<T> {
	let lastError: Error;

	for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
		try {
			return await operation();
		} catch (error) {
			lastError = error as Error;

			// Check if error is retryable
			const isRetryable = isErrorRetryable(error as Error, config);

			if (!isRetryable || attempt === config.maxAttempts) {
				// Log final failure
				await logError(error as Error, {
					...context,
					metadata: {
						...context.metadata,
						attempt,
						maxAttempts: config.maxAttempts,
						finalFailure: true,
					},
				});
				throw error;
			}

			// Calculate delay with exponential backoff
			const delay = Math.min(
				config.baseDelay * config.backoffMultiplier ** (attempt - 1),
				config.maxDelay,
			);

			// Add jitter to prevent thundering herd
			const jitteredDelay = delay + Math.random() * 1000;

			console.warn(
				`Attempt ${attempt} failed, retrying in ${jitteredDelay}ms:`,
				error,
			);

			await new Promise((resolve) => setTimeout(resolve, jitteredDelay));
		}
	}

	throw lastError!;
}

/**
 * Check if an error is retryable based on configuration
 */
function isErrorRetryable(error: Error, config: RetryConfig): boolean {
	if (error instanceof SystemError) {
		return error.retryable && config.retryableErrors.includes(error.code);
	}

	if (error instanceof ConvexError) {
		const errorCode = error.data?.code || "UNKNOWN_ERROR";
		return config.retryableErrors.includes(errorCode);
	}

	// Check common error patterns
	const errorMessage = error.message.toLowerCase();

	if (errorMessage.includes("network") || errorMessage.includes("timeout")) {
		return config.retryableErrors.includes("NETWORK_ERROR");
	}

	if (errorMessage.includes("rate limit")) {
		return config.retryableErrors.includes("RATE_LIMITED");
	}

	if (
		errorMessage.includes("service unavailable") ||
		errorMessage.includes("502") ||
		errorMessage.includes("503")
	) {
		return config.retryableErrors.includes("SERVICE_UNAVAILABLE");
	}

	return false;
}

/**
 * Log error to database and console
 */
export async function logError(
	error: Error,
	context: ErrorContext,
	ctx?: { db?: { insert: (table: string, doc: unknown) => Promise<unknown> } },
): Promise<void> {
	let severity: "low" | "medium" | "high" | "critical" = "medium";

	// Determine severity
	if (error instanceof SystemError) {
		severity = error.severity;
	} else if (error instanceof AIProviderError) {
		severity = "high";
	} else if (error instanceof AgentStateError) {
		severity = "high";
	}

	const errorLog = {
		errorType: error.name || "Error",
		errorMessage: error.message,
		stackTrace: error.stack,
		userId: context.userId,
		sessionId: context.sessionId,
		route: context.operation,
		severity,
		resolved: false,
		createdAt: Date.now(),
		metadata: context.metadata,
	};

	try {
		if (ctx?.db) {
			await ctx.db.insert("errorLogs", errorLog);
		}
	} catch (dbError) {
		console.error("Failed to log error to database:", dbError);
	}

	// Console logging with appropriate level
	switch (severity) {
		case "critical":
			console.error("CRITICAL ERROR:", error, context);
			break;
		case "high":
			console.error("HIGH SEVERITY ERROR:", error, context);
			break;
		case "medium":
			console.warn("ERROR:", error, context);
			break;
		case "low":
			console.info("LOW SEVERITY ERROR:", error, context);
			break;
	}
}

/**
 * AI Provider failover logic
 */
export async function executeWithAIFailover<T>(
	primaryOperation: () => Promise<T>,
	fallbackOperation: () => Promise<T>,
	context: ErrorContext,
): Promise<T> {
	try {
		return await retryWithBackoff(primaryOperation, RETRY_CONFIGS.ai_request, {
			...context,
			operation: `${context.operation}_primary`,
		});
	} catch (primaryError) {
		console.warn(
			"Primary AI provider failed, attempting fallback:",
			primaryError,
		);

		try {
			return await retryWithBackoff(
				fallbackOperation,
				RETRY_CONFIGS.ai_request,
				{ ...context, operation: `${context.operation}_fallback` },
			);
		} catch {
			// Both providers failed
			await logError(
				new SystemError(
					"AI_FAILOVER_EXHAUSTED",
					"Both primary and fallback AI providers failed",
					context,
					{ severity: "critical" },
				),
				context,
			);

			throw new SystemError(
				"AI_PROVIDERS_UNAVAILABLE",
				"All AI providers are currently unavailable. Please try again later.",
				context,
				{ severity: "critical" },
			);
		}
	}
}

/**
 * Agent state recovery
 */
export async function recoverAgentState(
	ctx: {
		db: {
			get: (id: string) => Promise<{
				status?: string;
				progress?: { percentComplete: number };
			} | null>;
			query: (table: string) => {
				withIndex: (
					index: string,
					fn: (q: { eq: (field: string, value: string) => unknown }) => unknown,
				) => {
					first: () => Promise<{ status?: string } | null>;
					order: (direction: string) => {
						take: (limit: number) => Promise<unknown[]>;
					};
				};
			};
		};
	},
	sessionId: string,
	agentId?: string,
): Promise<{
	recovered: boolean;
	state?: unknown;
	actions?: string[];
}> {
	try {
		// Get session and agent data
		const session = await ctx.db.get(sessionId);
		if (!session) {
			return { recovered: false, actions: ["create_new_session"] };
		}

		let agent: {status?: string } | null = null;
		if (agentId) {
			agent = await ctx.db.get(agentId);
		} else {
			agent = await ctx.db
				.query("agents")
				.withIndex(
					"by_session",
					(q: { eq: (field: string, value: string) => unknown }) =>
						q.eq("sessionId", sessionId),
				)
				.first();
		}

		// Get recent messages for context
		const messages = await ctx.db
			.query("messages")
			.withIndex(
				"by_session",
				(q: { eq: (field: string, value: string) => unknown }) =>
					q.eq("sessionId", sessionId),
			)
			.order("desc")
			.take(10);

		const recoveryActions: string[] = [];

		// Check agent state
		if (!agent) {
			recoveryActions.push("initialize_agent");
		} else if (agent.status === "error") {
			recoveryActions.push("reset_agent_status");
		}

		// Check session state
		if (session.status === "abandoned") {
			recoveryActions.push("reactivate_session");
		}

		// Check for incomplete workflows
		if (
			session.progress?.percentComplete &&
			session.progress.percentComplete < 100 &&
			session.progress.percentComplete > 0
		) {
			recoveryActions.push("resume_workflow");
		}

		return {
			recovered: true,
			state: {
				session,
				agent,
				recentMessages: messages.reverse(),
			},
			actions: recoveryActions,
		};
	} catch (error) {
		await logError(error as Error, {
			operation: "agent_state_recovery",
			sessionId,
			agentId,
			timestamp: Date.now(),
		});

		return { recovered: false, actions: ["full_system_reset"] };
	}
}

/**
 * Input validation and sanitization
 */
export function validateAndSanitizeInput(
	input: unknown,
	rules: {
		required?: boolean;
		type?: "string" | "number" | "boolean" | "object";
		minLength?: number;
		maxLength?: number;
		pattern?: RegExp;
		sanitize?: boolean;
	},
): { isValid: boolean; sanitized?: unknown; errors: string[] } {
	const errors: string[] = [];
	let sanitized = input;

	// Required check
	if (
		rules.required &&
		(input === undefined || input === null || input === "")
	) {
		errors.push("Field is required");
		return { isValid: false, errors };
	}

	// Skip further validation if not required and empty
	if (
		!rules.required &&
		(input === undefined || input === null || input === "")
	) {
		return { isValid: true, sanitized: input, errors: [] };
	}

	// Type validation
	if (rules.type && typeof input !== rules.type) {
		errors.push(`Expected ${rules.type}, got ${typeof input}`);
	}

	if (rules.type === "string" && typeof input === "string") {
		// Length validation
		if (rules.minLength && input.length < rules.minLength) {
			errors.push(`Minimum length is ${rules.minLength}`);
		}

		if (rules.maxLength && input.length > rules.maxLength) {
			errors.push(`Maximum length is ${rules.maxLength}`);
		}

		// Pattern validation
		if (rules.pattern && !rules.pattern.test(input)) {
			errors.push("Invalid format");
		}

		// Sanitization
		if (rules.sanitize) {
			// Basic HTML/script sanitization
			sanitized = input
				.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, "")
				.replace(/<[^>]*>/g, "")
				.trim();
		}
	}

	return {
		isValid: errors.length === 0,
		sanitized,
		errors,
	};
}

/**
 * Graceful degradation helper
 */
export async function withGracefulDegradation<T>(
	primaryOperation: () => Promise<T>,
	fallbackValue: T,
	context: ErrorContext,
): Promise<T> {
	try {
		return await primaryOperation();
	} catch (error) {
		await logError(error as Error, {
			...context,
			metadata: { ...context.metadata, gracefulDegradation: true },
		});

		return fallbackValue;
	}
}
