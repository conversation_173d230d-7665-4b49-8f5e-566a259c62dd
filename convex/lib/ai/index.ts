import {
	generateGeneralResponse,
	isClaudeConfigured,
	calculateClaudeCost,
} from "./anthropic";
import {
	generateGeneralResponseOpenAI,
	isOpenAIConfigured,
	calculateOpenAICost,
} from "./openai";
import { formatPersonalityResponse, GENERAL_PERSONALITY } from "./personality";

/**
 * AI Provider Integration Layer
 *
 * Orchestrates AI provider selection, rate limiting, cost tracking,
 * and fallback logic for TheGeneral agent responses.
 */

export interface AIResponse {
	content: string;
	provider: "anthropic" | "openai";
	model: string;
	tokensUsed: {
		prompt: number;
		completion: number;
		total: number;
	};
	responseTime: number;
	cost: number; // USD
}

export interface AIContext {
	projectName: string;
	currentStep: string;
	conversationHistory: string[];
	userName: string;
	sessionId: string;
}

/**
 * Generate AI response with provider fallback and cost tracking
 */
export async function generateResponse(
	userMessage: string,
	context: AIContext,
	preferences: {
		preferredProvider?: "anthropic" | "openai";
		maxTokens?: number;
		temperature?: number;
	} = {},
): Promise<AIResponse> {
	// Determine provider order based on preferences and availability
	const providers = getProviderOrder(preferences.preferredProvider);

	let lastError: Error | null = null;

	// Try each provider in order
	for (const provider of providers) {
		try {
			const response = await generateResponseWithProvider(
				userMessage,
				context,
				provider,
				{
					maxTokens: preferences.maxTokens || 1000,
					temperature: preferences.temperature || 0.7,
				},
			);

			return response;
		} catch (error) {
			lastError = error as Error;
			console.warn(`AI provider ${provider} failed:`, error);
			continue;
		}
	}

	// If all providers failed
	throw new Error(
		`All AI providers failed. Last error: ${lastError?.message || "Unknown error"}`,
	);
}

/**
 * Generate response using specific provider
 */
async function generateResponseWithProvider(
	userMessage: string,
	context: AIContext,
	provider: "anthropic" | "openai",
	config: { maxTokens: number; temperature: number },
): Promise<AIResponse> {
	if (provider === "anthropic" && isClaudeConfigured()) {
		const response = await generateGeneralResponse(userMessage, context, {
			model: "claude-3-haiku-20240307",
			maxTokens: config.maxTokens,
			temperature: config.temperature,
		});

		return {
			content: response.content,
			provider: "anthropic",
			model: response.model,
			tokensUsed: response.tokensUsed,
			responseTime: response.responseTime,
			cost: calculateClaudeCost(response.tokensUsed),
		};
	}

	if (provider === "openai" && isOpenAIConfigured()) {
		const response = await generateGeneralResponseOpenAI(userMessage, context, {
			model: "gpt-3.5-turbo",
			maxTokens: config.maxTokens,
			temperature: config.temperature,
		});

		return {
			content: response.content,
			provider: "openai",
			model: response.model,
			tokensUsed: response.tokensUsed,
			responseTime: response.responseTime,
			cost: calculateOpenAICost(response.tokensUsed),
		};
	}

	throw new Error(`Provider ${provider} is not configured or available`);
}

/**
 * Get provider order based on preferences and availability
 */
function getProviderOrder(
	preferredProvider?: "anthropic" | "openai",
): ("anthropic" | "openai")[] {
	const availableProviders: ("anthropic" | "openai")[] = [];

	// Add available providers
	if (isClaudeConfigured()) availableProviders.push("anthropic");
	if (isOpenAIConfigured()) availableProviders.push("openai");

	if (availableProviders.length === 0) {
		throw new Error(
			"No AI providers are configured. Please set ANTHROPIC_API_KEY or OPENAI_API_KEY",
		);
	}

	// Prioritize preferred provider if specified and available
	if (preferredProvider && availableProviders.includes(preferredProvider)) {
		return [
			preferredProvider,
			...availableProviders.filter((p) => p !== preferredProvider),
		];
	}

	// Default order: Claude first (typically better for personality), then OpenAI
	return availableProviders.sort((a, b) => {
		if (a === "anthropic") return -1;
		if (b === "anthropic") return 1;
		return 0;
	});
}

/**
 * Enhanced response generation with personality formatting
 */
export async function generatePersonalityResponse(
	userMessage: string,
	context: AIContext,
	responseType:
		| "greeting"
		| "question"
		| "acknowledgment"
		| "correction"
		| "completion",
	preferences: {
		preferredProvider?: "anthropic" | "openai";
		maxTokens?: number;
		temperature?: number;
	} = {},
): Promise<AIResponse> {
	const response = await generateResponse(userMessage, context, preferences);

	// Format response with personality consistency
	const formattedContent = formatPersonalityResponse(
		response.content,
		responseType,
		GENERAL_PERSONALITY,
	);

	return {
		...response,
		content: formattedContent,
	};
}

/**
 * Check if any AI provider is available
 */
export function isAIAvailable(): boolean {
	return isClaudeConfigured() || isOpenAIConfigured();
}

/**
 * Get available providers
 */
export function getAvailableProviders(): ("anthropic" | "openai")[] {
	const providers: ("anthropic" | "openai")[] = [];

	if (isClaudeConfigured()) providers.push("anthropic");
	if (isOpenAIConfigured()) providers.push("openai");

	return providers;
}

/**
 * Estimate cost for a request before making it
 */
export function estimateRequestCost(
	messageLength: number,
	provider: "anthropic" | "openai" = "anthropic",
): number {
	// Rough token estimation: ~4 characters per token
	const estimatedTokens = Math.ceil(messageLength / 4);
	const systemPromptTokens = 500; // Approximate system prompt size
	const responseTokens = 800; // Typical response size

	const totalPromptTokens = estimatedTokens + systemPromptTokens;
	const totalCompletionTokens = responseTokens;

	if (provider === "anthropic") {
		return calculateClaudeCost({
			prompt: totalPromptTokens,
			completion: totalCompletionTokens,
			total: totalPromptTokens + totalCompletionTokens,
		});
	} else {
		return calculateOpenAICost({
			prompt: totalPromptTokens,
			completion: totalCompletionTokens,
			total: totalPromptTokens + totalCompletionTokens,
		});
	}
}
