import { anthropic } from "@ai-sdk/anthropic";
import { generateText } from "ai";
import { validateUsageData } from "./utils";

/**
 * Anthropic Claude Integration
 *
 * Provides Claude integration for TheGeneral agent with
 * drill instructor personality and cost tracking.
 */

export interface ClaudeConfig {
	model: string;
	maxTokens: number;
	temperature: number;
}

export interface ClaudeResponse {
	content: string;
	tokensUsed: {
		prompt: number;
		completion: number;
		total: number;
	};
	responseTime: number;
	model: string;
}

/**
 * Default Claude configuration for TheGeneral agent
 */
export const DEFAULT_CLAUDE_CONFIG: ClaudeConfig = {
	model: "claude-3-haiku-20240307", // Cost-effective model for agent responses
	maxTokens: 1000,
	temperature: 0.7,
};

/**
 * Parse environment variable as float with fallback to default
 */
function parseEnvFloat(envVar: string | undefined, defaultValue: number): number {
	if (!envVar) return defaultValue;
	const parsed = parseFloat(envVar);
	return Number.isNaN(parsed) ? defaultValue : parsed;
}

/**
 * Claude pricing configuration with environment variable support
 * Updated rates as of 2024: $0.0008 per 1K input tokens, $0.004 per 1K output tokens
 */
export const CLAUDE_PRICING = {
	// Input token cost per 1K tokens (default: $0.0008)
	inputCostPer1K: parseEnvFloat(process.env.CLAUDE_INPUT_COST_PER_1K, 0.0008),
	// Output token cost per 1K tokens (default: $0.004)
	outputCostPer1K: parseEnvFloat(process.env.CLAUDE_OUTPUT_COST_PER_1K, 0.004),
};

/**
 * Generate response using Claude with drill instructor personality
 */
export async function generateClaudeResponse(
	prompt: string,
	systemPrompt: string,
	config: ClaudeConfig = DEFAULT_CLAUDE_CONFIG,
): Promise<ClaudeResponse> {
	const startTime = Date.now();

	try {
		const result = await generateText({
			model: anthropic(config.model),
			prompt,
			system: systemPrompt,
			maxTokens: config.maxTokens,
			temperature: config.temperature,
		});

		const responseTime = Date.now() - startTime;

		// Validate usage data and provide defaults if missing
		const tokensUsed = validateUsageData(result.usage);

		return {
			content: result.text,
			tokensUsed,
			responseTime,
			model: config.model,
		};
	} catch (error) {
		throw new Error(
			`Claude API error: ${error instanceof Error ? error.message : "Unknown error"}`,
		);
	}
}

/**
 * Generate drill instructor response for TheGeneral agent
 */
export async function generateGeneralResponse(
	userMessage: string,
	context: {
		projectName: string;
		currentStep: string;
		conversationHistory: string[];
		userName: string;
	},
	config: ClaudeConfig = DEFAULT_CLAUDE_CONFIG,
): Promise<ClaudeResponse> {
	const systemPrompt = `You are General Thompson, a drill instructor helping users create comprehensive Product Requirements Documents (PRDs).

PERSONALITY TRAITS:
- Military drill instructor style - firm but supportive
- Uses military terminology and motivational language
- Demands precision and attention to detail
- Provides structured, menu-driven interactions
- Maintains high energy and urgency
- Shows approval for good answers, corrects poor ones

COMMUNICATION STYLE:
- Use ALL CAPS for emphasis and commands
- Include military emojis (🎯, 🪖, 📋, 🔧, etc.)
- Always provide numbered menu options [1], [2], [3]
- Keep responses focused and actionable
- Ask one clear question at a time
- Acknowledge good answers with military praise

CURRENT CONTEXT:
- Project: ${context.projectName}
- Current Phase: ${context.currentStep}
- User: ${context.userName}

CONVERSATION HISTORY:
${context.conversationHistory.slice(-5).join("\n")}

Remember: You're helping create a PROFESSIONAL PRD while maintaining the drill instructor character. Be tough but helpful!`;

	const prompt = `User message: "${userMessage}"

Respond as General Thompson, maintaining the drill instructor personality while guiding the PRD creation process. Provide a structured response with clear next steps and numbered options.`;

	return await generateClaudeResponse(prompt, systemPrompt, config);
}

/**
 * Calculate estimated cost for Claude usage
 */
export function calculateClaudeCost(tokensUsed: {
	prompt: number;
	completion: number;
	total: number;
}): number {
	const promptCost = (tokensUsed.prompt / 1000) * CLAUDE_PRICING.inputCostPer1K;
	const completionCost = (tokensUsed.completion / 1000) * CLAUDE_PRICING.outputCostPer1K;

	return promptCost + completionCost; // Returns cost in USD
}

/**
 * Check if Claude API key is configured
 */
export function isClaudeConfigured(): boolean {
	return !!process.env.ANTHROPIC_API_KEY;
}
