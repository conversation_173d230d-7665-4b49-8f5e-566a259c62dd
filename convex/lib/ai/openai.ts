import { openai } from "@ai-sdk/openai";
import { generateText } from "ai";
import { validateUsageData } from "./utils";

/**
 * OpenAI GPT Integration
 *
 * Provides OpenAI GPT fallback integration for TheGeneral agent
 * when <PERSON> is unavailable or rate limited.
 */

export interface OpenAIConfig {
	model: string;
	maxTokens: number;
	temperature: number;
}

export interface OpenAIResponse {
	content: string;
	tokensUsed: {
		prompt: number;
		completion: number;
		total: number;
	};
	responseTime: number;
	model: string;
}

/**
 * Default OpenAI configuration for TheGeneral agent
 */
export const DEFAULT_OPENAI_CONFIG: OpenAIConfig = {
	model: "gpt-3.5-turbo", // Cost-effective model for agent responses
	maxTokens: 1000,
	temperature: 0.7,
};

/**
 * OpenAI pricing configuration with environment variable support
 * GPT-3.5-Turbo rates: $0.0005 per 1K input tokens, $0.0015 per 1K output tokens
 */
export const OPENAI_PRICING = {
	// Input token cost per 1K tokens (default: $0.0005)
	inputCostPer1K: parseFloat(process.env.OPENAI_INPUT_COST_PER_1K || "0.0005"),
	// Output token cost per 1K tokens (default: $0.0015)
	outputCostPer1K: parseFloat(process.env.OPENAI_OUTPUT_COST_PER_1K || "0.0015"),
};

/**
 * Generate response using OpenAI GPT with drill instructor personality
 */
export async function generateOpenAIResponse(
	prompt: string,
	systemPrompt: string,
	config: OpenAIConfig = DEFAULT_OPENAI_CONFIG,
): Promise<OpenAIResponse> {
	const startTime = Date.now();

	try {
		const result = await generateText({
			model: openai(config.model),
			prompt,
			system: systemPrompt,
			maxTokens: config.maxTokens,
			temperature: config.temperature,
		});

		const responseTime = Date.now() - startTime;

		// Validate usage data and provide defaults if missing
		const tokensUsed = validateUsageData(result.usage);

		return {
			content: result.text,
			tokensUsed,
			responseTime,
			model: config.model,
		};
	} catch (error) {
		throw new Error(
			`OpenAI API error: ${error instanceof Error ? error.message : "Unknown error"}`,
		);
	}
}

/**
 * Generate drill instructor response for TheGeneral agent using OpenAI
 */
export async function generateGeneralResponseOpenAI(
	userMessage: string,
	context: {
		projectName: string;
		currentStep: string;
		conversationHistory: string[];
		userName: string;
	},
	config: OpenAIConfig = DEFAULT_OPENAI_CONFIG,
): Promise<OpenAIResponse> {
	const systemPrompt = `You are General Thompson, a drill instructor helping users create comprehensive Product Requirements Documents (PRDs). 

PERSONALITY TRAITS:
- Military drill instructor style - firm but supportive
- Uses military terminology and motivational language
- Demands precision and attention to detail  
- Provides structured, menu-driven interactions
- Maintains high energy and urgency
- Shows approval for good answers, corrects poor ones

COMMUNICATION STYLE:
- Use ALL CAPS for emphasis and commands
- Include military emojis (🎯, 🪖, 📋, 🔧, etc.)
- Always provide numbered menu options [1], [2], [3]
- Keep responses focused and actionable
- Ask one clear question at a time
- Acknowledge good answers with military praise

CURRENT CONTEXT:
- Project: ${context.projectName}
- Current Phase: ${context.currentStep}
- User: ${context.userName}

CONVERSATION HISTORY:
${context.conversationHistory.slice(-5).join("\n")}

Remember: You're helping create a PROFESSIONAL PRD while maintaining the drill instructor character. Be tough but helpful!`;

	const prompt = `User message: "${userMessage}"

Respond as General Thompson, maintaining the drill instructor personality while guiding the PRD creation process. Provide a structured response with clear next steps and numbered options.`;

	return await generateOpenAIResponse(prompt, systemPrompt, config);
}

/**
 * Calculate estimated cost for OpenAI usage
 */
export function calculateOpenAICost(tokensUsed: {
	prompt: number;
	completion: number;
	total: number;
}): number {
	const promptCost = (tokensUsed.prompt / 1000) * OPENAI_PRICING.inputCostPer1K;
	const completionCost = (tokensUsed.completion / 1000) * OPENAI_PRICING.outputCostPer1K;

	return promptCost + completionCost; // Returns cost in USD
}

/**
 * Check if OpenAI API key is configured
 */
export function isOpenAIConfigured(): boolean {
	return !!process.env.OPENAI_API_KEY;
}
