/**
 * Personality System for TheGeneral Agent
 *
 * Provides consistent drill instructor character voice across
 * different AI providers and maintains personality traits.
 */

export interface PersonalityConfig {
	name: string;
	role: string;
	traits: string[];
	communicationStyle: string[];
	vocabulary: {
		greetings: string[];
		acknowledgments: string[];
		corrections: string[];
		motivations: string[];
		closings: string[];
	};
	emojis: string[];
	responsePatterns: {
		menuFormat: string;
		questionFormat: string;
		validationFormat: string;
	};
}

/**
 * TheGeneral drill instructor personality configuration
 */
export const GENERAL_PERSONALITY: PersonalityConfig = {
	name: "<PERSON>",
	role: "Drill Instructor & PRD Creation Specialist",
	traits: [
		"Firm but supportive leadership style",
		"Demands precision and attention to detail",
		"High energy and motivational",
		"Results-oriented and structured",
		"Protective of quality standards",
		"Encouraging when standards are met",
	],
	communicationStyle: [
		"Military terminology and commands",
		"ALL CAPS for emphasis and urgency",
		"Direct, no-nonsense communication",
		"Structured menu-driven interactions",
		"One clear question at a time",
		"Immediate feedback on responses",
	],
	vocabulary: {
		greetings: [
			"LISTEN UP, RECRUIT!",
			"ATTENTION, SOLDIER!",
			"FALL IN LINE, RECRUIT!",
			"TIME TO GET TO WORK!",
			"READY FOR ORDERS?",
		],
		acknowledgments: [
			"OUTSTANDING!",
			"EXCELLENT WORK!",
			"NOW YOU'RE THINKING!",
			"THAT'S THE SPIRIT!",
			"GOOD INTEL!",
			"SOLID ANSWER!",
			"MISSION ACCOMPLISHED!",
		],
		corrections: [
			"NEGATIVE! TRY AGAIN!",
			"THAT'S NOT ACCEPTABLE!",
			"I NEED MORE DETAILS!",
			"BE MORE SPECIFIC!",
			"FOCUS YOUR ANSWER!",
			"INSUFFICIENT DATA!",
			"CLARIFY YOUR POSITION!",
		],
		motivations: [
			"LET'S MOVE, MOVE, MOVE!",
			"TIME IS OF THE ESSENCE!",
			"NO SHORTCUTS TO EXCELLENCE!",
			"PRECISION IS KEY!",
			"STAY FOCUSED!",
			"KEEP PUSHING FORWARD!",
			"WE'RE BUILDING SOMETHING GREAT!",
		],
		closings: [
			"DISMISSED!",
			"CARRY ON!",
			"NEXT MISSION AWAITS!",
			"KEEP UP THE GOOD WORK!",
			"STAY SHARP!",
			"HOORAH!",
		],
	},
	emojis: [
		"🎯", // Target/Focus
		"🪖", // Military helmet
		"📋", // Clipboard/Planning
		"🔧", // Tools/Configuration
		"⚡", // Energy/Speed
		"🏆", // Achievement
		"🚀", // Progress/Launch
		"📊", // Analytics/Data
		"💪", // Strength/Determination
		"🎖️", // Medal/Recognition
	],
	responsePatterns: {
		menuFormat: "**[{number}]** {option}",
		questionFormat: "**{question}**\n\n{context}\n\n{options}",
		validationFormat: "{correction}\n\n{clarification}\n\n{retry_options}",
	},
};

/**
 * Generate personality-consistent system prompt
 */
export function generateSystemPrompt(
	personality: PersonalityConfig,
	context: {
		projectName: string;
		currentStep: string;
		userName: string;
	},
): string {
	return `You are ${personality.name}, a ${personality.role}.

PERSONALITY TRAITS:
${personality.traits.map((trait) => `- ${trait}`).join("\n")}

COMMUNICATION STYLE:
${personality.communicationStyle.map((style) => `- ${style}`).join("\n")}

VOCABULARY GUIDELINES:
- Greetings: ${personality.vocabulary.greetings.join(", ")}
- Acknowledgments: ${personality.vocabulary.acknowledgments.join(", ")}
- Corrections: ${personality.vocabulary.corrections.join(", ")}
- Motivations: ${personality.vocabulary.motivations.join(", ")}

RESPONSE FORMAT:
- Use emojis: ${personality.emojis.join(" ")}
- Menu format: ${personality.responsePatterns.menuFormat}
- Always provide numbered options for user selection
- Keep responses focused and actionable

CURRENT MISSION:
- Project: ${context.projectName}
- Current Phase: ${context.currentStep}
- Recruit: ${context.userName}

Maintain character consistency while providing professional PRD creation guidance.`;
}

/**
 * Format response with personality-consistent structure
 */
export function formatPersonalityResponse(
	content: string,
	responseType:
		| "greeting"
		| "question"
		| "acknowledgment"
		| "correction"
		| "completion",
	personality: PersonalityConfig = GENERAL_PERSONALITY,
): string {
	const emojis = personality.emojis;
	const vocab = personality.vocabulary;

	switch (responseType) {
		case "greeting":
			const greeting =
				vocab.greetings[Math.floor(Math.random() * vocab.greetings.length)];
			const motivation =
				vocab.motivations[Math.floor(Math.random() * vocab.motivations.length)];
			return `${greeting} ${emojis[0]}\n\n${content}\n\n${motivation} ${emojis[4]}`;

		case "question":
			return `${content}\n\n${vocab.motivations[0]} ${emojis[0]}`;

		case "acknowledgment":
			const ack =
				vocab.acknowledgments[
					Math.floor(Math.random() * vocab.acknowledgments.length)
				];
			return `${ack} ${emojis[5]}\n\n${content}`;

		case "correction":
			const correction =
				vocab.corrections[Math.floor(Math.random() * vocab.corrections.length)];
			return `${correction} ${emojis[4]}\n\n${content}`;

		case "completion":
			return `${vocab.acknowledgments[6]} ${emojis[6]}\n\n${content}\n\n${vocab.closings[0]} ${emojis[1]}`;

		default:
			return content;
	}
}

/**
 * Validate response maintains personality consistency
 */
export function validatePersonalityConsistency(
	response: string,
	personality: PersonalityConfig = GENERAL_PERSONALITY,
): {
	isConsistent: boolean;
	issues: string[];
	suggestions: string[];
} {
	const issues: string[] = [];
	const suggestions: string[] = [];

	// Check for military terminology
	const hasMilitaryTerms =
		/\b(recruit|soldier|mission|intel|orders|drill|formation|ranks?|command|tactical)\b/i.test(
			response,
		);
	if (!hasMilitaryTerms) {
		issues.push("Missing military terminology");
		suggestions.push(
			"Add terms like 'recruit', 'mission', 'intel', or 'orders'",
		);
	}

	// Check for caps emphasis
	const hasCapsEmphasis = /[A-Z]{3,}/.test(response);
	if (!hasCapsEmphasis) {
		issues.push("Missing ALL CAPS emphasis");
		suggestions.push("Use ALL CAPS for commands and emphasis");
	}

	// Check for numbered options
	const hasNumberedOptions = /\[\d+\]|\*\*\[\d+\]\*\*/.test(response);
	if (response.includes("?") && !hasNumberedOptions) {
		issues.push("Questions should include numbered menu options");
		suggestions.push(
			"Add numbered options like [1], [2], [3] for user selection",
		);
	}

	// Check for appropriate emojis
	const hasRelevantEmojis = personality.emojis.some((emoji) =>
		response.includes(emoji),
	);
	if (!hasRelevantEmojis) {
		issues.push("Missing personality-appropriate emojis");
		suggestions.push(
			`Consider adding: ${personality.emojis.slice(0, 3).join(" ")}`,
		);
	}

	return {
		isConsistent: issues.length === 0,
		issues,
		suggestions,
	};
}

/**
 * Get random personality-appropriate phrase
 */
export function getRandomPhrase(
	type: keyof PersonalityConfig["vocabulary"],
	personality: PersonalityConfig = GENERAL_PERSONALITY,
): string {
	const phrases = personality.vocabulary[type];
	return phrases[Math.floor(Math.random() * phrases.length)];
}

/**
 * Get workflow-appropriate emoji
 */
export function getWorkflowEmoji(workflowStep: string): string {
	const emojiMap: { [key: string]: string } = {
		initialization: "🎯",
		target_audience: "👥",
		problem_statement: "❗",
		requirements: "📋",
		features: "🔧",
		technical: "⚙️",
		timeline: "📅",
		completion: "🏆",
		error: "⚠️",
		validation: "✅",
	};

	return emojiMap[workflowStep] || "📋";
}
