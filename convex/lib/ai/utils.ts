/**
 * AI Integration Utilities
 *
 * Shared utilities for AI provider integrations, including
 * usage data validation and token calculation helpers.
 */

export interface UsageData {
	promptTokens?: number;
	completionTokens?: number;
	totalTokens?: number;
}

export interface ValidatedUsage {
	prompt: number;
	completion: number;
	total: number;
}

/**
 * Validate and normalize usage data from AI providers
 *
 * Provides safe defaults for missing usage data and calculates
 * total tokens if not provided by the AI provider.
 *
 * @param usage - Raw usage data from AI provider (may be undefined or incomplete)
 * @returns Validated usage data with all fields guaranteed to be numbers
 */
export function validateUsageData(usage?: UsageData): ValidatedUsage {
	// Safely extract token counts with defaults
	const promptTokens = usage?.promptTokens ?? 0;
	const completionTokens = usage?.completionTokens ?? 0;
	const totalTokens = usage?.totalTokens ?? promptTokens + completionTokens;

	return {
		prompt: promptTokens,
		completion: completionTokens,
		total: totalTokens,
	};
}

/**
 * Calculate the cost of API usage based on token counts
 *
 * @param usage - Validated usage data
 * @param inputCostPer1k - Cost per 1000 input tokens in USD
 * @param outputCostPer1k - Cost per 1000 output tokens in USD
 * @returns Total cost in USD
 */
export function calculateUsageCost(
	usage: ValidatedUsage,
	inputCostPer1k: number,
	outputCostPer1k: number,
): number {
	const inputCost = (usage.prompt / 1000) * inputCostPer1k;
	const outputCost = (usage.completion / 1000) * outputCostPer1k;
	return inputCost + outputCost;
}
