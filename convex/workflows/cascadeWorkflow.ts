import { createThread, saveMessage } from "@convex-dev/agent";
import { getAuthUserId } from "@convex-dev/auth/server";
import { WorkflowManager } from "@convex-dev/workflow";
import { v } from "convex/values";
import { z } from "zod";
import { components, internal } from "../_generated/api";
import { internalAction, mutation } from "../_generated/server";
import { focusGateAgent } from "../agents/focusGate";
import { responseGenAgent } from "../agents/responseGen";
import { scopeGateAgent } from "../agents/scopeGate";
import { specificityGateAgent } from "../agents/specificityGate";
import { vaguenessGateAgent } from "../agents/vaguenessGate";

const workflow = new WorkflowManager(components.workflow);

export const cascadeWorkflow = workflow.define({
	args: { userInput: v.string(), userId: v.string(), userThreadId: v.string() },
	handler: async (step, { userInput, userId, userThreadId }): Promise<void> => {
		// Create internal thread for gate analysis (not visible to user)
		const { threadId: internalThreadId } = await step.runMutation(
			internal.workflows.cascadeWorkflow.createInternalThread,
			{
				userId,
				title: `Internal Analysis: ${userInput.slice(0, 50)}...`,
			},
		);

		// Step 1: Check vagueness on internal thread
		const vaguenessResult = await step.runAction(
			internal.workflows.cascadeWorkflow.checkVagueness,
			{
				userId,
				threadId: internalThreadId,
				prompt: userInput,
			},
			{ retry: true },
		);

		// Step 2: Check focus on internal thread
		const focusResult = await step.runAction(
			internal.workflows.cascadeWorkflow.checkFocus,
			{
				userId,
				threadId: internalThreadId,
				prompt: userInput,
			},
			{ retry: true },
		);

		// Step 3: Check scope on internal thread
		const scopeResult = await step.runAction(
			internal.workflows.cascadeWorkflow.checkScope,
			{
				userId,
				threadId: internalThreadId,
				prompt: userInput,
			},
			{ retry: true },
		);

		// Step 4: Check specificity on internal thread
		const specificityResult = await step.runAction(
			internal.workflows.cascadeWorkflow.checkSpecificity,
			{
				userId,
				threadId: internalThreadId,
				prompt: userInput,
			},
			{ retry: true },
		);

		// Step 5: Trigger streaming response after workflow completes
		await step.runAction(internal.workflows.cascadeWorkflow.generateResponse, {
			userId,
			threadId: userThreadId,
			userInput,
			gateResults: {
				vagueness: vaguenessResult,
				focus: focusResult,
				scope: scopeResult,
				specificity: specificityResult,
			},
		});
	},
});

export const startWorkflow = mutation({
	args: { userInput: v.string(), threadId: v.optional(v.string()) },
	handler: async (
		ctx,
		{ userInput, threadId: existingThreadId },
	): Promise<{ threadId: string; workflowId: string }> => {
		const userId = await getAuthUserId(ctx);
		if (!userId) {
			throw new Error("Authentication required");
		}

		// Use existing thread or create new one for user-visible messages
		const userThreadId =
			existingThreadId ||
			(await createThread(ctx, components.agent, {
				userId: userId,
				title: `PRD Analysis: ${userInput.slice(0, 50)}...`,
			}));

		// Save the user's message to the user thread
		await saveMessage(ctx, components.agent, {
			threadId: userThreadId,
			prompt: userInput,
		});

		// Start workflow with both user input and user thread ID
		const workflowId = await workflow.start(
			ctx,
			internal.workflows.cascadeWorkflow.cascadeWorkflow,
			{ userInput, userId, userThreadId },
		);

		return { threadId: userThreadId, workflowId };
	},
});

const gateResultSchema = z.object({
	passed: z.boolean(),
	reason: z.string(),
	confidence: z.number().min(0).max(1).optional(),
});

export const checkVagueness = vaguenessGateAgent.asObjectAction({
	schema: gateResultSchema,
	maxSteps: 3,
});

export const checkFocus = focusGateAgent.asTextAction({
	maxSteps: 3,
});

export const checkScope = scopeGateAgent.asTextAction({
	maxSteps: 3,
});

export const checkSpecificity = specificityGateAgent.asTextAction({
	maxSteps: 3,
});

// Streaming response action that runs outside the workflow
export const generateResponse = internalAction({
	args: {
		userId: v.string(),
		threadId: v.string(),
		userInput: v.string(),
		gateResults: v.any(),
	},
	handler: async (ctx, args) => {
		const analysisContext = `
Gate Analysis Results:
- Vagueness: ${JSON.stringify(args.gateResults.vagueness)}
- Focus: ${JSON.stringify(args.gateResults.focus)}
- Scope: ${JSON.stringify(args.gateResults.scope)}
- Specificity: ${JSON.stringify(args.gateResults.specificity)}

User Input: "${args.userInput}"

Based on this analysis, provide a helpful response that addresses the user's request and incorporates insights from the gate analyses.
		`;

		// Use responseGenAgent.streamText with correct signature
		const result = await responseGenAgent.streamText(
			ctx,
			{
				userId: args.userId,
				threadId: args.threadId,
			},
			{
				messages: [
					{
						role: "user",
						content: analysisContext,
					},
				],
			},
			{
				saveStreamDeltas: true,
			},
		);

		// Consume the stream to ensure it completes
		let fullText = "";
		try {
			for await (const textPart of result.textStream) {
				fullText += textPart;
			}
			return { success: true, messageId: result.messageId, text: fullText };
		} catch (error) {
			console.error("Error consuming text stream:", error);
			return {
				success: false,
				error: error instanceof Error ? error.message : "Unknown stream error",
				messageId: result.messageId,
			};
		}
	},
});

export const createInternalThread = responseGenAgent.createThreadMutation();
