/**
 * Shared types and interfaces for the PRD Generation Gate System
 *
 * This file defines the core types used across all gate functions and the cascade workflow.
 * These types ensure consistency and type safety throughout the gate validation pipeline.
 */

import { v } from "convex/values";
import type { Id } from "../_generated/dataModel";

/**
 * Result returned by each gate function
 */
export type GateResult = {
	/** Whether the gate passed or failed */
	status: "pass" | "fail";
	/** Optional message explaining the result (required for failures) */
	message?: string;
	/** Gate-specific metadata */
	metadata?: Record<string, string | number | boolean | string[]>;
};

/**
 * Validation schema for GateResult
 */
export const gateResultValidator = v.object({
	status: v.union(v.literal("pass"), v.literal("fail")),
	message: v.optional(v.string()),
	metadata: v.optional(
		v.record(
			v.string(),
			v.union(v.string(), v.number(), v.boolean(), v.array(v.string())),
		),
	),
});

/**
 * Context passed to each gate function
 */
export type GateContext = {
	/** The user's input message */
	userInput: string;
	/** Conversation ID for tracking */
	conversationId: Id<"conversations">;
	/** Previous messages in the conversation */
	previousMessages?: Array<{
		role: "user" | "assistant" | "system";
		content: string;
		createdAt: number;
	}>;
	/** User ID (optional for anonymous users) */
	userId?: string;
	/** Additional context from previous gates */
	priorGateResults: Record<string, DetailedGateResult>;
};

/**
 * Specific reasons why a gate might fail
 */
export type GateFailureReason =
	// Vagueness Gate failures
	| "technical_jargon_without_context"
	| "solution_first_language"
	| "architecture_buzzwords_without_problem"
	| "lacks_specific_context"
	| "too_broad_or_generic"
	| "ambiguous_references"

	// Focus Gate failures
	| "multiple_products_detected"
	| "disparate_ideas_mixed"
	| "comparison_request"
	| "multiple_domains"

	// Scope Gate failures
	| "insufficient_detail"
	| "unclear_requirements"
	| "missing_context"
	| "too_many_features"
	| "multiple_core_functions"
	| "ecosystem_scale_detected"
	| "kitchen_sink_approach"
	| "scope_creep"

	// Specificity Gate failures
	| "missing_problem_statement"
	| "missing_target_user"
	| "missing_core_feature"
	| "missing_success_metrics"
	| "refinement_loop_exhausted"

	// Feasibility Gate failures
	| "not_feasible";

/**
 * Validation schema for GateFailureReason
 */
export const gateFailureReasonValidator = v.union(
	// Vagueness Gate failures
	v.literal("technical_jargon_without_context"),
	v.literal("solution_first_language"),
	v.literal("architecture_buzzwords_without_problem"),
	v.literal("lacks_specific_context"),
	v.literal("too_broad_or_generic"),
	v.literal("ambiguous_references"),

	// Focus Gate failures
	v.literal("multiple_products_detected"),
	v.literal("disparate_ideas_mixed"),
	v.literal("comparison_request"),
	v.literal("multiple_domains"),

	// Scope Gate failures
	v.literal("insufficient_detail"),
	v.literal("unclear_requirements"),
	v.literal("missing_context"),
	v.literal("too_many_features"),
	v.literal("multiple_core_functions"),
	v.literal("ecosystem_scale_detected"),
	v.literal("kitchen_sink_approach"),
	v.literal("scope_creep"),

	// Specificity Gate failures
	v.literal("missing_target_user"),
	v.literal("missing_core_feature"),
	v.literal("missing_success_metrics"),
	v.literal("missing_problem_statement"),
	v.literal("refinement_loop_exhausted"),

	// Feasibility Gate failures
	v.literal("not_feasible"),
);

/**
 * Enhanced gate result with failure reasons
 */
export type DetailedGateResult = GateResult & {
	/** Specific reasons for failure (if status is "fail") */
	failureReasons?: GateFailureReason[];
	/** Confidence score (0-1) */
	confidence?: number;
	/** Processing time in milliseconds */
	processingTime?: number;
	/** Additional metadata */
	metadata?: Record<string, unknown>;
};

/**
 * Validation schema for DetailedGateResult
 */
export const detailedGateResultValidator = v.object({
	status: v.union(v.literal("pass"), v.literal("fail")),
	message: v.optional(v.string()),
	metadata: v.optional(
		v.record(
			v.string(),
			v.union(v.string(), v.number(), v.boolean(), v.array(v.string())),
		),
	),
	failureReasons: v.optional(v.array(gateFailureReasonValidator)),
	confidence: v.optional(v.number()),
	processingTime: v.optional(v.number()),
});

/**
 * Validation schema for GateContext
 */
export const gateContextValidator = v.object({
	userInput: v.string(),
	conversationId: v.string(),
	previousMessages: v.optional(
		v.array(
			v.object({
				role: v.union(
					v.literal("user"),
					v.literal("assistant"),
					v.literal("system"),
				),
				content: v.string(),
				createdAt: v.number(),
			}),
		),
	),
	userId: v.optional(v.string()),
	priorGateResults: v.record(v.string(), detailedGateResultValidator),
});

/**
 * Overall cascade flow result
 */
export type CascadeFlowResult = {
	/** Final status of the cascade */
	status: "pass" | "fail" | "error";
	/** The gate that caused failure (if any) */
	failedGate?: "vagueness" | "focus" | "scope" | "specificity";
	/** Results from each gate that was executed */
	gateResults: Record<string, DetailedGateResult>;
	/** Final message to return to user */
	message: string;
	/** Total processing time */
	totalProcessingTime: number;
	/** Conversation mode after cascade */
	conversationMode: "clarification" | "generation";
};

/**
 * Validation schema for CascadeFlowResult
 */
export const cascadeFlowResultValidator = v.object({
	status: v.union(v.literal("pass"), v.literal("fail"), v.literal("error")),
	failedGate: v.optional(
		v.union(
			v.literal("vagueness"),
			v.literal("focus"),
			v.literal("scope"),
			v.literal("specificity"),
		),
	),
	gateResults: v.record(v.string(), detailedGateResultValidator),
	message: v.string(),
	totalProcessingTime: v.number(),
	conversationMode: v.union(
		v.literal("clarification"),
		v.literal("generation"),
	),
});

/**
 * Configuration for the refinement loop in the Specificity Gate
 */
export type RefinementConfig = {
	/** Maximum number of refinement iterations */
	maxIterations: number;
	/** Current iteration number */
	currentIteration: number;
	/** Questions asked in previous iterations */
	previousQuestions: string[];
	/** User responses from previous iterations */
	previousResponses: string[];
};

/**
 * Validation schema for RefinementConfig
 */
export const refinementConfigValidator = v.object({
	maxIterations: v.number(),
	currentIteration: v.number(),
	previousQuestions: v.array(v.string()),
	previousResponses: v.array(v.string()),
});

/**
 * Gate execution order and configuration
 */
export const GATE_EXECUTION_ORDER = [
	"vagueness",
	"focus",
	"scope",
	"specificity",
] as const;

export type GateName = (typeof GATE_EXECUTION_ORDER)[number];

/**
 * Default configuration values
 */
export const DEFAULT_CONFIG = {
	MAX_REFINEMENT_ITERATIONS: 3,
	GATE_TIMEOUT_MS: 30000,
	CONFIDENCE_THRESHOLD: 0.7,
} as const;

/**
 * Legacy types for backward compatibility
 */
export type GateConfig = {
	name: string;
	enabled: boolean;
	weight: number;
	timeout?: number;
	retries?: number;
};

export type CascadeResult = {
	status: "pass" | "fail";
	message: string;
	gateResults: Record<string, DetailedGateResult>;
	totalProcessingTime: number;
	failedAt?: string;
};
