import { v } from "convex/values";
import { mutation, query } from "../_generated/server";
import { PRD_QUESTIONNAIRE_WORKFLOW } from "../lib/workflows/prd_questionnaire";

/**
 * PRD Session Management Functions
 *
 * Handles creation and management of PRD sessions for agent-guided
 * PRD creation with TheGeneral agent.
 */

/**
 * Calculate total required steps from workflow configuration
 */
function calculateTotalSteps(): number {
	return PRD_QUESTIONNAIRE_WORKFLOW.filter((step) => step.isRequired).length;
}

/**
 * Maximum allowed size for PRD content in bytes (1MB)
 */
const MAX_PRD_CONTENT_SIZE = 1024 * 1024; // 1MB

/**
 * Validate content size against a specified limit
 */
function validateContentSize(
	content: string,
	maxSize: number = MAX_PRD_CONTENT_SIZE,
	contentType: string = "PRD content",
): void {
	// Calculate byte length using TextEncoder for compatibility
	const contentSizeBytes = new TextEncoder().encode(content).length;
	if (contentSizeBytes > maxSize) {
		throw new Error(
			`${contentType} size (${Math.round(contentSizeBytes / 1024)}KB) exceeds maximum allowed size (${Math.round(maxSize / 1024)}KB)`,
		);
	}
}

/**
 * Create a new PRD session for agent-guided PRD creation
 */
export const createSession = mutation({
	args: {
		userId: v.string(),
		projectName: v.string(),
		targetAudience: v.optional(v.string()),
	},
	handler: async (ctx, args) => {
		// Validate project name is not empty
		if (!args.projectName || args.projectName.trim() === "") {
			throw new Error("Project name cannot be empty");
		}

		const now = Date.now();

		// Calculate total steps dynamically from workflow configuration
		const totalSteps = calculateTotalSteps();

		// Create initial PRD session
		const sessionId = await ctx.db.insert("prdSessions", {
			userId: args.userId,
			projectName: args.projectName.trim(),
			targetAudience: args.targetAudience,
			status: "active",
			currentStep: "initialization",
			questionnaire: {},
			progress: {
				completedSteps: [],
				totalSteps: totalSteps,
				percentComplete: 0,
			},
			createdAt: now,
			updatedAt: now,
		});

		return sessionId;
	},
});

/**
 * Get current PRD session with real-time subscription support
 */
export const getSession = query({
	args: {
		sessionId: v.id("prdSessions"),
	},
	handler: async (ctx, args) => {
		const session = await ctx.db.get(args.sessionId);

		if (!session) {
			return null;
		}

		// Get associated agent if exists
		const agent = await ctx.db
			.query("agents")
			.withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
			.first();

		// Get recent messages for context
		const messages = await ctx.db
			.query("messages")
			.withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
			.order("desc")
			.take(50);

		return {
			session,
			agent,
			messages: messages.reverse(), // Chronological order
		};
	},
});

/**
 * Update questionnaire step and notify agent
 */
export const updateQuestionnaireStep = mutation({
	args: {
		sessionId: v.id("prdSessions"),
		stepName: v.string(),
		stepData: v.any(),
		markCompleted: v.optional(v.boolean()),
	},
	handler: async (ctx, args) => {
		const session = await ctx.db.get(args.sessionId);

		if (!session) {
			throw new Error("Session not found");
		}

		const now = Date.now();

		// Update questionnaire data
		const updatedQuestionnaire = {
			...session.questionnaire,
			[args.stepName]: args.stepData,
		};

		// Update completed steps if marked as completed
		let updatedProgress = session.progress;
		if (
			args.markCompleted &&
			!session.progress.completedSteps.includes(args.stepName)
		) {
			const completedSteps = [
				...session.progress.completedSteps,
				args.stepName,
			];
			updatedProgress = {
				...session.progress,
				completedSteps,
				percentComplete: Math.round(
					(completedSteps.length / session.progress.totalSteps) * 100,
				),
			};
		}

		// Update session
		await ctx.db.patch(args.sessionId, {
			currentStep: args.stepName,
			questionnaire: updatedQuestionnaire,
			progress: updatedProgress,
			updatedAt: now,
		});

		// Update agent's last active time if agent exists
		const agent = await ctx.db
			.query("agents")
			.withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
			.first();

		if (agent) {
			await ctx.db.patch(agent._id, {
				lastActiveAt: now,
			});
		}

		return {
			sessionId: args.sessionId,
			stepName: args.stepName,
			progress: updatedProgress,
		};
	},
});

/**
 * Get all sessions for a user
 */
export const getUserSessions = query({
	args: {
		userId: v.string(),
	},
	handler: async (ctx, args) => {
		const sessions = await ctx.db
			.query("prdSessions")
			.withIndex("by_user", (q) => q.eq("userId", args.userId))
			.order("desc")
			.collect();

		return sessions;
	},
});

/**
 * Update session status
 */
export const updateSessionStatus = mutation({
	args: {
		sessionId: v.id("prdSessions"),
		status: v.union(
			v.literal("active"),
			v.literal("paused"),
			v.literal("completed"),
			v.literal("abandoned"),
		),
	},
	handler: async (ctx, args) => {
		await ctx.db.patch(args.sessionId, {
			status: args.status,
			updatedAt: Date.now(),
		});

		return { sessionId: args.sessionId, status: args.status };
	},
});

/**
 * Add generated PRD content to session
 */
export const addGeneratedPRD = mutation({
	args: {
		sessionId: v.id("prdSessions"),
		content: v.string(),
		format: v.union(v.literal("markdown"), v.literal("json")),
		version: v.string(),
	},
	handler: async (ctx, args) => {
		// Validate content size before proceeding
		validateContentSize(args.content);

		const now = Date.now();

		await ctx.db.patch(args.sessionId, {
			generatedPRD: {
				content: args.content,
				format: args.format,
				generatedAt: now,
				version: args.version,
			},
			status: "completed",
			updatedAt: now,
		});

		return { sessionId: args.sessionId, version: args.version };
	},
});
