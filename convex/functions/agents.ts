import { v } from "convex/values";
import type { Doc, Id } from "../_generated/dataModel";
import { type MutationCtx, mutation, query } from "../_generated/server";

/**
 * TheGeneral Agent Core Functions
 *
 * Implements the drill instructor agent for PRD creation guidance
 * with personality-driven interactions and workflow integration.
 */

/**
 * Get the next sequence number for a session to ensure message ordering
 */
async function getNextSequenceNumber(
	ctx: MutationCtx,
	sessionId: Id<"prdSessions">,
): Promise<number> {
	const lastMessage = await ctx.db
		.query("messages")
		.withIndex("by_session_sequence", (q) => q.eq("sessionId", sessionId))
		.order("desc")
		.first();

	return (lastMessage?.sequenceNumber ?? 0) + 1;
}

/**
 * Initialize TheGeneral agent with drill instructor persona
 */
export const initializeGeneral = mutation({
	args: {
		sessionId: v.id("prdSessions"),
	},
	handler: async (ctx, args) => {
		const session = await ctx.db.get(args.sessionId);

		if (!session) {
			throw new Error("PRD session not found");
		}

		const now = Date.now();

		// Create TheGeneral agent
		const agentId = await ctx.db.insert("agents", {
			sessionId: args.sessionId,
			type: "TheGeneral",
			personality: "drill_instructor",
			status: "active",
			currentWorkflow: "prd_questionnaire",
			context: {
				userName: session.userId,
				projectName: session.projectName,
				motivationLevel: "high",
				currentPhase: "initialization",
			},
			memory: {
				userPreferences: {},
				conversationSummary: "Starting PRD creation process",
				motivationLevel: "high",
			},
			metrics: {
				messagesGenerated: 0,
				avgResponseTime: 0,
				tokensUsed: 0,
			},
			createdAt: now,
			lastActiveAt: now,
		});

		// Send initial greeting message with sequence number
		const initialSequenceNumber = await getNextSequenceNumber(
			ctx,
			args.sessionId,
		);
		await ctx.db.insert("messages", {
			sessionId: args.sessionId,
			senderId: "TheGeneral",
			senderType: "agent",
			content: `LISTEN UP, RECRUIT! I'm General Thompson, and I'm here to whip your PRD into shape! You want to build "${session.projectName}"? Outstanding! But we're gonna do this RIGHT, and we're gonna do it BY THE BOOK!

🎯 **MISSION BRIEFING:**
- We're creating a COMPREHENSIVE Product Requirements Document
- NO shortcuts, NO half-measures, NO "good enough"
- Every detail matters, every requirement counts
- I'll ask the tough questions, you give me PRECISE answers

**READY TO BEGIN THE DRILL?**

**[1]** Yes sir! Let's create this PRD!
**[2]** I need to understand the process first
**[3]** Can we customize the approach?

Pick your number and let's MOVE, MOVE, MOVE! 🪖`,
			messageType: "greeting",
			metadata: {
				agentPersonality: "drill_instructor",
				workflowStep: "initialization",
				confidence: 1.0,
			},
			timestamp: now,
			sequenceNumber: initialSequenceNumber,
		});

		return agentId;
	},
});

/**
 * Send user message and trigger agent workflow
 */
export const sendMessage = mutation({
	args: {
		sessionId: v.id("prdSessions"),
		senderId: v.string(),
		content: v.string(),
		messageType: v.optional(
			v.union(
				v.literal("greeting"),
				v.literal("question"),
				v.literal("response"),
				v.literal("validation"),
				v.literal("completion"),
			),
		),
	},
	handler: async (ctx, args) => {
		const session = await ctx.db.get(args.sessionId);
		const agent = await ctx.db
			.query("agents")
			.withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
			.first();

		if (!session || !agent) {
			throw new Error("Session or agent not found");
		}

		const now = Date.now();

		// Get sequence numbers for reliable message ordering
		const userSequenceNumber = await getNextSequenceNumber(ctx, args.sessionId);
		const agentSequenceNumber = userSequenceNumber + 1;

		// Store user message
		await ctx.db.insert("messages", {
			sessionId: args.sessionId,
			senderId: args.senderId,
			senderType: "user",
			content: args.content,
			messageType: args.messageType || "response",
			metadata: {},
			timestamp: now,
			sequenceNumber: userSequenceNumber,
		});

		// Update agent metrics
		await ctx.db.patch(agent._id, {
			lastActiveAt: now,
			metrics: {
				...agent.metrics!,
				messagesGenerated: (agent.metrics?.messagesGenerated || 0) + 1,
			},
		});

		// Trigger agent response based on workflow step
		const agentResponse = await generateAgentResponse(
			ctx,
			session,
			agent,
			args.content,
		);

		// Store agent response
		await ctx.db.insert("messages", {
			sessionId: args.sessionId,
			senderId: "TheGeneral",
			senderType: "agent",
			content: agentResponse.content,
			messageType: agentResponse.messageType,
			metadata: {
				agentPersonality: "drill_instructor",
				workflowStep: agentResponse.workflowStep,
				confidence: agentResponse.confidence,
			},
			timestamp: now,
			sequenceNumber: agentSequenceNumber,
		});

		return {
			sessionId: args.sessionId,
			agentResponse: agentResponse.content,
			workflowStep: agentResponse.workflowStep,
		};
	},
});

/**
 * Get conversation history for real-time chat
 */
export const getConversation = query({
	args: {
		sessionId: v.id("prdSessions"),
		limit: v.optional(v.number()),
	},
	handler: async (ctx, args) => {
		const messages = await ctx.db
			.query("messages")
			.withIndex("by_session_sequence", (q) =>
				q.eq("sessionId", args.sessionId),
			)
			.order("asc")
			.take(args.limit || 100);

		const agent = await ctx.db
			.query("agents")
			.withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
			.first();

		return {
			messages,
			agent,
		};
	},
});

/**
 * Generate agent response based on workflow and context
 */
async function generateAgentResponse(
	ctx: MutationCtx,
	session: Doc<"prdSessions">,
	agent: Doc<"agents">,
	userInput: string,
): Promise<{
	content: string;
	messageType:
		| "greeting"
		| "question"
		| "response"
		| "validation"
		| "completion";
	workflowStep: string;
	confidence: number;
}> {
	// Parse user input for menu selections
	const menuSelection =
		userInput.trim().match(/^\[?(\d+)\]?$/) ||
		userInput.trim().match(/^(\d+)$/);

	// Determine current workflow step
	const currentStep = session.currentStep;

	if (currentStep === "initialization") {
		if (menuSelection) {
			const choice = parseInt(menuSelection[1]);
			switch (choice) {
				case 1:
					return {
						content: `EXCELLENT CHOICE, RECRUIT! Time to get down to BUSINESS!

🎯 **PHASE 1: PROJECT FOUNDATION**

First, I need intel on your target audience. This isn't some wishy-washy "everyone" nonsense - I want SPECIFICS!

**WHO exactly will use "${session.projectName}"?**

Give me details like:
- Demographics (age, profession, experience level)
- Their current pain points
- Why they need YOUR solution
- What they're using now (competitors)

**[1]** B2B professionals in [specific industry]
**[2]** B2C consumers with [specific need]
**[3]** Internal teams/employees
**[4]** Technical users (developers, admins)
**[5]** Mixed audience (explain the segments)

SPEAK UP! What's your target audience? 🎯`,
						messageType: "question",
						workflowStep: "target_audience",
						confidence: 1.0,
					};

				case 2:
					return {
						content: `GOOD! A smart soldier asks questions before charging into battle!

📋 **THE PRD CREATION PROCESS - BATTLE PLAN:**

**Phase 1: Foundation** (Where we are now)
- Target audience identification
- Problem statement definition
- Success metrics establishment

**Phase 2: Core Requirements**
- Feature specifications
- User stories and acceptance criteria
- Technical requirements

**Phase 3: Implementation Details**
- Architecture considerations
- Timeline and milestones
- Resource requirements

**Phase 4: Final Assembly**
- Risk assessment
- Testing strategy
- Launch plan

Each phase builds on the last - NO SKIPPING AHEAD!

**Ready to begin Phase 1?**
**[1]** Yes, let's start with target audience
**[2]** I want to see a sample PRD first
**[3]** Can we modify this process?

What's it gonna be, recruit? 📋`,
						messageType: "question",
						workflowStep: "process_explanation",
						confidence: 1.0,
					};

				case 3:
					return {
						content: `CUSTOMIZATION? I respect that tactical thinking, soldier!

🔧 **AVAILABLE MODIFICATIONS:**

**Audience Focus:**
**[A]** Skip audience deep-dive (for internal projects)
**[B]** Extended market research phase
**[C]** Competitive analysis emphasis

**Detail Level:**
**[D]** Rapid prototype PRD (minimal viable)
**[E]** Comprehensive enterprise PRD
**[F]** Technical specification focus

**Industry Specific:**
**[G]** SaaS/Software product
**[H]** Mobile application
**[I]** Hardware/IoT product
**[J]** Service/Platform business

Pick your modifications (can choose multiple: A,D,G) or:
**[1]** Standard process works fine
**[2]** Let me think about this

WHAT'S YOUR TACTICAL PREFERENCE? 🔧`,
						messageType: "question",
						workflowStep: "customization",
						confidence: 0.9,
					};

				default:
					return {
						content: `THAT'S NOT A VALID OPTION, RECRUIT!

I gave you THREE clear choices:
**[1]** Yes sir! Let's create this PRD!
**[2]** I need to understand the process first
**[3]** Can we customize the approach?

Pick a NUMBER between 1 and 3. Don't make me repeat myself!

TRY AGAIN! 🎯`,
						messageType: "validation",
						workflowStep: "initialization",
						confidence: 1.0,
					};
			}
		} else {
			return {
				content: `I NEED A CLEAR ANSWER, RECRUIT!

You can either:
- Type **[1]**, **[2]**, or **[3]**
- Or just type **1**, **2**, or **3**

Don't give me paragraphs when I need NUMBERS!

**[1]** Yes sir! Let's create this PRD!
**[2]** I need to understand the process first
**[3]** Can we customize the approach?

PICK YOUR NUMBER! 🎯`,
				messageType: "validation",
				workflowStep: "initialization",
				confidence: 1.0,
			};
		}
	}

	// Default response for unhandled cases
	return {
		content: `HOLD UP, RECRUIT! I'm processing your input...

"${userInput}"

Let me get you back on track. What's your current situation?

**[1]** Start over with target audience
**[2]** Continue with current phase
**[3]** Need help understanding something

GIVE ME A NUMBER! 🎯`,
		messageType: "question",
		workflowStep: "recovery",
		confidence: 0.7,
	};
}

/**
 * Update agent status
 */
export const updateAgentStatus = mutation({
	args: {
		agentId: v.id("agents"),
		status: v.union(
			v.literal("active"),
			v.literal("paused"),
			v.literal("completed"),
			v.literal("error"),
		),
	},
	handler: async (ctx, args) => {
		// Validate that the agent exists before updating
		const agent = await ctx.db.get(args.agentId);
		if (!agent) {
			throw new Error(`Agent with ID ${args.agentId} not found`);
		}

		await ctx.db.patch(args.agentId, {
			status: args.status,
			lastActiveAt: Date.now(),
		});

		return { agentId: args.agentId, status: args.status };
	},
});

/**
 * Get agent by session
 */
export const getAgentBySession = query({
	args: {
		sessionId: v.id("prdSessions"),
	},
	handler: async (ctx, args) => {
		return await ctx.db
			.query("agents")
			.withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
			.first();
	},
});
