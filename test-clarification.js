/**
 * Test script to verify the clarification engine behavior
 */

async function testClarificationEngine() {
  const baseUrl = 'http://localhost:3002';
  
  console.log('🧪 Testing PRDGeneral Clarification Engine\n');

  // Test 1: Vague product idea (should push back)
  console.log('Test 1: Vague product idea');
  console.log('Input: "I want to build a social media app"');
  
  try {
    const response1 = await fetch(`${baseUrl}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages: [
          { role: 'user', content: 'I want to build a social media app' }
        ]
      })
    });

    if (response1.ok) {
      const reader = response1.body.getReader();
      const decoder = new TextDecoder();
      let result = '';
      
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        result += decoder.decode(value);
      }
      
      console.log('Response:', result.slice(0, 200) + '...\n');
    } else {
      console.log('Error:', response1.status, response1.statusText);
    }
  } catch (error) {
    console.log('Error:', error.message);
  }

  // Test 2: PRD generation trigger without complete clarification (should stay in clarification)
  console.log('Test 2: PRD generation trigger (incomplete clarification)');
  console.log('Input: "Generate PRD" without complete clarification');

  try {
    const response2 = await fetch(`${baseUrl}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages: [
          { role: 'user', content: 'I have a product idea' },
          { role: 'assistant', content: 'Great! Tell me about it.' },
          { role: 'user', content: 'Generate PRD' }
        ]
      })
    });

    if (response2.ok) {
      const reader = response2.body.getReader();
      const decoder = new TextDecoder();
      let result = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        result += decoder.decode(value);
      }

      console.log('Response:', result.slice(0, 200) + '...\n');
    } else {
      console.log('Error:', response2.status, response2.statusText);
    }
  } catch (error) {
    console.log('Error:', error.message);
  }

  // Test 3: Complete clarification flow then PRD generation
  console.log('Test 3: Complete clarification flow + PRD generation');
  console.log('Input: Full conversation with clarification completion');

  try {
    const response3 = await fetch(`${baseUrl}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages: [
          { role: 'user', content: 'I want to build a task management app for freelancers' },
          { role: 'assistant', content: 'Let\'s get more specific about the problem you\'re solving...' },
          { role: 'user', content: 'Freelancers struggle to track billable hours across multiple clients' },
          { role: 'assistant', content: 'Good! Now what\'s the ONE core feature...' },
          { role: 'user', content: 'A simple time tracker that automatically categorizes work by client' },
          { role: 'assistant', content: 'Perfect! Who is your specific target user?' },
          { role: 'user', content: 'Solo freelancers who work with 3-5 clients simultaneously' },
          { role: 'assistant', content: 'Excellent! How will you measure success?' },
          { role: 'user', content: 'Users track at least 80% of their billable hours within first week' },
          { role: 'assistant', content: 'That\'s clear! You\'ve completed all 4 steps.' },
          { role: 'user', content: 'Generate PRD' }
        ]
      })
    });

    if (response3.ok) {
      const reader = response3.body.getReader();
      const decoder = new TextDecoder();
      let result = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        result += decoder.decode(value);
      }

      console.log('Response:', result.slice(0, 300) + '...\n');
    } else {
      console.log('Error:', response3.status, response3.statusText);
    }
  } catch (error) {
    console.log('Error:', error.message);
  }

  // Test 4: Refinement mode trigger
  console.log('Test 4: Refinement mode trigger');
  console.log('Input: "Refine the PRD"');

  try {
    const response4 = await fetch(`${baseUrl}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages: [
          { role: 'user', content: 'Here is my PRD...' },
          { role: 'assistant', content: '# Executive Summary\n\nTime tracking app for freelancers...' },
          { role: 'user', content: 'Refine the PRD to focus more on mobile usage' }
        ]
      })
    });

    if (response4.ok) {
      const reader = response4.body.getReader();
      const decoder = new TextDecoder();
      let result = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        result += decoder.decode(value);
      }

      console.log('Response:', result.slice(0, 200) + '...\n');
    } else {
      console.log('Error:', response4.status, response4.statusText);
    }
  } catch (error) {
    console.log('Error:', error.message);
  }

  // Test 5: API health check
  console.log('Test 5: API health check');

  try {
    const response5 = await fetch(`${baseUrl}/api/chat`, {
      method: 'GET'
    });

    if (response5.ok) {
      const data = await response5.json();
      console.log('API Status:', data);
    } else {
      console.log('Error:', response5.status, response5.statusText);
    }
  } catch (error) {
    console.log('Error:', error.message);
  }

  console.log('\n✅ Mode switching tests completed!');
  console.log('Expected behaviors:');
  console.log('- Test 1: Should push back on vague ideas (clarification mode)');
  console.log('- Test 2: Should stay in clarification mode despite "Generate PRD" trigger');
  console.log('- Test 3: Should switch to generation mode after complete clarification');
  console.log('- Test 4: Should switch to refinement mode for PRD improvements');
  console.log('- Test 5: Should return API health status');
}

// Run the tests
testClarificationEngine().catch(console.error);
