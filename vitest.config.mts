import { defineConfig } from "vitest/config";
import react from '@vitejs/plugin-react';
import tsconfigPaths from 'vite-tsconfig-paths';

export default defineConfig({
  plugins: [react(), tsconfigPaths()],
  test: {
    environment: "jsdom",
    server: { deps: { inline: ["convex-test"] } },
    // Test files in both convex and src folders
    include: ["convex/**/*.test.{ts,js}", "src/**/*.test.{ts,js,tsx}"],
    globals: true, // Enable global test functions like describe, it, expect
    setupFiles: ['./src/__tests__/setup.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json-summary', 'lcov'],
      reportsDirectory: './coverage',
    },
  },
});