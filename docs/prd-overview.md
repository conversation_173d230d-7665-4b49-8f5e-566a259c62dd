# PRDGeneral - Overview

## 1. Goals and Background Context

* **Mission:** To reduce the time and effort required to create a high-quality PRD by using a "General" persona agent that cajoles, motivates, and improves the user's productivity.
* **Target Audience:** Developers and "solopreneur" types, primarily focused on creating web-based applications.
* **Success Criteria:** The MVP will be considered successful if it can guide a user through an interactive, questionnaire-style process to produce a complete PRD.
* **Key Architectural Mandate:** The system must be built on a Next.js + Convex stack. For the MVP, it will be a single, monolithic agent ("<PERSON><PERSON>eneral").

## 2. Requirements

### Functional Requirements
1.  **FR1:** The agent, "TheGeneral," must consistently maintain the personality of a drill instructor.
2.  **FR2:** TheGeneral must guide the user through a structured, interactive, menu-driven questionnaire to gather PRD information.
3.  **FR3:** The final output must be a complete, well-structured PRD document in Markdown format, displayed directly in the chat interface.

### Non-Functional Requirements
1.  **NFR1:** The application must be built using Next.js for the frontend and Convex for the backend.
2.  **NFR2:** The user experience must be productive and motivating.
3.  **NFR3:** The agent's operations should be designed for cost-effectiveness.