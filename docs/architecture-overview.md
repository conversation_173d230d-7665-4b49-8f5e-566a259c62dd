# PRDGeneral - System Overview

## 1. Introduction

This document outlines the complete fullstack architecture for **PRDGeneral**. It is a greenfield project being built from a custom scratch structure.

## 2. High-Level Architecture

* **Technical Summary:** The architecture is a modern, serverless web application featuring a Next.js frontend and a Convex backend managing the database, serverless functions, and the core logic for "TheGeneral" agent.
* **Platform Choice:** Frontend hosting on Vercel; Backend infrastructure on Convex.
* **Repository Structure:** A monorepo containing both frontend and backend code.

### High-Level Architecture Diagram

```mermaid
graph TD
    User -->|Interacts with UI| NextJS[Next.js Frontend on Vercel]
    NextJS -->|Calls Functions & Subscribes to Data| Convex[Convex Backend]
    Convex -->|Real-time Updates| NextJS
    Convex -- manages --> DB[(Database)]
    Convex -- executes --> Agent[Agent Logic]
```

## 3. Tech Stack

| Category            | Technology                  | Rationale                                                                 |
|---------------------|----------------------------|--------------------------------------------------------------------------|
| Language            | TypeScript                 | Adds type safety to JavaScript, which is crucial for agent-written code.  |
| Frontend Framework  | Next.js (React)            | A modern, performant framework for web applications.                      |
| Backend Platform    | Convex                     | Handles the database, serverless functions, and agent logic.              |
| Styling             | Tailwind CSS               | A utility-first CSS framework perfect for creating clean, minimalist designs quickly. |
| State Management    | React Hooks + Convex Hooks | A simple, powerful approach that avoids premature complexity.             |
| Testing             | Vitest & React Testing Library | A modern, fast test runner compatible with Next.js and Convex backend functions. |
| CI/CD               | Vercel CI/CD               | Tightly integrated with Vercel hosting for seamless, automatic deployments. |
| Dev Environment     | Devbox                     | Provides an isolated, reproducible development environment.               |
| Package Manager     | pnpm (recommended)         | A fast, disk space-efficient package manager.                             |