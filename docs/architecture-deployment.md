# PRDGeneral - Deployment & Development

## 6. Project Structure and Workflows

### Unified Project Structure

```plaintext
/PRDGeneral
├── convex/
│   ├── schema.ts
│   ├── auth.config.ts
│   └── messages.ts
├── src/
│   ├── app/
│   ├── components/
│   └── lib/
├── public/
├── docs/
├── .env.local
├── package.json
└── tsconfig.json
```

### Development Workflow

1. **Prerequisites:** Node.js, Devbox, pnpm.
2. **Start shell:** `devbox shell`
3. **Install dependencies:** `pnpm install`
4. **Run backend:** `npx convex dev`
5. **Run frontend:** `pnpm run dev`

### Deployment Architecture

- **Frontend:** Deployed to Vercel via Git push to the main branch.
- **Backend:** Deployed via the Convex CLI: `npx convex deploy`.
- **CI/CD:** Handled by Vercel's built-in pipeline.

## 7. Guiding Principles

- **Security:** Handled by Convex Auth and mandatory authorization checks in every backend function.
- **Performance:** Leverages Next.js optimizations, Vercel's Edge Network, and Convex's real-time architecture.
- **Testing:** Backend functions tested with Vitest in the `convex/tests/` directory. Frontend components tested with Vitest and React Testing Library. E2E tests are out of scope for the MVP.
- **Coding Standards:** Enforced with ESLint and Prettier.
- **Error Handling:** React Error Boundaries on the frontend, try...catch blocks on the backend.
- **Monitoring:** Vercel Analytics for the frontend, Convex Dashboard for the backend.