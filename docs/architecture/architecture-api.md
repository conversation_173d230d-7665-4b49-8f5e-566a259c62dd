# API Specification & Core Workflows

## Convex API Structure

PRDGeneral uses Convex's type-safe API architecture with functions, queries, and mutations. All API interactions are real-time and automatically synchronized across clients.

## Core API Endpoints

### Authentication APIs

#### `auth.getUserSession`
```typescript
// Query
export const getUserSession = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return null;
    
    return {
      userId: identity.subject,
      name: identity.name,
      email: identity.email,
      isAuthenticated: true
    };
  }
});
```

#### `auth.signOut`
```typescript
// Mutation
export const signOut = mutation({
  args: {},
  handler: async (ctx) => {
    await ctx.auth.signOut();
  }
});
```

### PRD Session Management

#### `prd.createSession`
```typescript
// Mutation - Initialize new PRD creation session
export const createSession = mutation({
  args: {
    projectName: v.string(),
    targetAudience: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Authentication required");

    const sessionId = await ctx.db.insert("prdSessions", {
      userId: identity.subject,
      projectName: args.projectName,
      targetAudience: args.targetAudience,
      status: "active",
      currentStep: "introduction",
      createdAt: Date.now(),
      updatedAt: Date.now(),
      questionnaire: {},
      generatedPRD: null,
    });

    // Initialize TheGeneral agent for this session
    await ctx.runMutation(api.agents.initializeGeneral, { sessionId });
    
    return sessionId;
  }
});
```

#### `prd.getSession`
```typescript
// Query - Get current PRD session
export const getSession = query({
  args: { sessionId: v.id("prdSessions") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return null;

    const session = await ctx.db.get(args.sessionId);
    if (!session || session.userId !== identity.subject) {
      return null;
    }

    return session;
  }
});
```

#### `prd.updateQuestionnaireStep`
```typescript
// Mutation - Update questionnaire responses
export const updateQuestionnaireStep = mutation({
  args: {
    sessionId: v.id("prdSessions"),
    step: v.string(),
    responses: v.record(v.string(), v.any()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Authentication required");

    const session = await ctx.db.get(args.sessionId);
    if (!session || session.userId !== identity.subject) {
      throw new Error("Session not found");
    }

    await ctx.db.patch(args.sessionId, {
      questionnaire: {
        ...session.questionnaire,
        [args.step]: args.responses,
      },
      currentStep: args.step,
      updatedAt: Date.now(),
    });

    // Notify TheGeneral agent of progress
    await ctx.runMutation(api.agents.processStep, {
      sessionId: args.sessionId,
      step: args.step,
      responses: args.responses,
    });
  }
});
```

### Agent Workflow APIs

#### `agents.initializeGeneral`
```typescript
// Mutation - Initialize TheGeneral agent
export const initializeGeneral = mutation({
  args: { sessionId: v.id("prdSessions") },
  handler: async (ctx, args) => {
    const agentId = await ctx.db.insert("agents", {
      sessionId: args.sessionId,
      type: "TheGeneral",
      personality: "drill_instructor",
      status: "active",
      currentWorkflow: "prd_questionnaire",
      context: {
        greetingDelivered: false,
        questionnairePhase: "introduction",
        userMotivationLevel: "neutral",
      },
      createdAt: Date.now(),
    });

    // Start the initial workflow
    await ctx.runMutation(api.workflows.startPRDQuestionnaire, {
      agentId,
      sessionId: args.sessionId,
    });

    return agentId;
  }
});
```

#### `agents.sendMessage`
```typescript
// Mutation - Send message to agent
export const sendMessage = mutation({
  args: {
    sessionId: v.id("prdSessions"),
    message: v.string(),
    messageType: v.union(v.literal("user_input"), v.literal("system_event")),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Authentication required");

    // Store user message
    const messageId = await ctx.db.insert("messages", {
      sessionId: args.sessionId,
      senderId: identity.subject,
      senderType: "user",
      content: args.message,
      messageType: args.messageType,
      timestamp: Date.now(),
    });

    // Get agent for this session
    const agent = await ctx.db
      .query("agents")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .first();

    if (agent) {
      // Process message through agent workflow
      await ctx.runMutation(api.workflows.processUserMessage, {
        agentId: agent._id,
        messageId,
        content: args.message,
      });
    }

    return messageId;
  }
});
```

#### `agents.getConversation`
```typescript
// Query - Get conversation history
export const getConversation = query({
  args: { sessionId: v.id("prdSessions") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return null;

    const session = await ctx.db.get(args.sessionId);
    if (!session || session.userId !== identity.subject) {
      return null;
    }

    const messages = await ctx.db
      .query("messages")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .order("asc")
      .collect();

    return messages;
  }
});
```

### AI Provider Integration

#### `ai.generateResponse`
```typescript
// Mutation - Generate AI response via external provider
export const generateResponse = mutation({
  args: {
    prompt: v.string(),
    context: v.record(v.string(), v.any()),
    provider: v.union(v.literal("anthropic"), v.literal("openai")),
    personality: v.string(),
  },
  handler: async (ctx, args) => {
    // Rate limiting
    await ctx.runMutation(api.rateLimiter.checkLimit, {
      identifier: "ai_generation",
      limit: 100, // 100 requests per hour
    });

    // Format prompt with personality and context
    const systemPrompt = formatPersonalityPrompt(args.personality);
    const fullPrompt = formatPromptWithContext(args.prompt, args.context);

    let response: string;
    
    if (args.provider === "anthropic") {
      response = await generateAnthropicResponse(systemPrompt, fullPrompt);
    } else {
      response = await generateOpenAIResponse(systemPrompt, fullPrompt);
    }

    return {
      response,
      provider: args.provider,
      timestamp: Date.now(),
      tokensUsed: estimateTokens(response),
    };
  }
});
```

## Core Application Workflows

### 1. PRD Creation Workflow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant ConvexAPI
    participant TheGeneral
    participant AIProvider

    User->>Frontend: Click "Create PRD"
    Frontend->>ConvexAPI: prd.createSession()
    ConvexAPI->>ConvexAPI: agents.initializeGeneral()
    ConvexAPI->>TheGeneral: Start PRD workflow
    TheGeneral->>AIProvider: Generate greeting
    AIProvider-->>TheGeneral: Drill instructor intro
    TheGeneral-->>ConvexAPI: Store agent message
    ConvexAPI-->>Frontend: Real-time update
    Frontend-->>User: Display agent greeting

    loop Questionnaire Process
        User->>Frontend: Answer question
        Frontend->>ConvexAPI: agents.sendMessage()
        ConvexAPI->>TheGeneral: Process user input
        TheGeneral->>AIProvider: Generate follow-up
        AIProvider-->>TheGeneral: Next question/response
        TheGeneral-->>ConvexAPI: Update workflow state
        ConvexAPI-->>Frontend: Real-time update
        Frontend-->>User: Display next interaction
    end

    TheGeneral->>AIProvider: Generate complete PRD
    AIProvider-->>TheGeneral: Formatted PRD document
    TheGeneral-->>ConvexAPI: Store final PRD
    ConvexAPI-->>Frontend: Real-time update
    Frontend-->>User: Display completed PRD
```

### 2. Session Recovery Workflow

```mermaid
flowchart TD
    UserReturns[User Returns to App] --> CheckAuth{Authenticated?}
    CheckAuth -->|No| LoginFlow[Redirect to Login]
    CheckAuth -->|Yes| GetActiveSessions[Query Active Sessions]
    
    GetActiveSessions --> HasSessions{Active Sessions?}
    HasSessions -->|No| ShowNewPRDOption[Show Create New PRD]
    HasSessions -->|Yes| ShowSessionList[Display Active Sessions]
    
    ShowSessionList --> UserSelectsSession[User Selects Session]
    UserSelectsSession --> LoadSession[Load Session State]
    LoadSession --> RestoreAgent[Restore Agent Context]
    RestoreAgent --> ContinueWorkflow[Continue from Last Step]
```

### 3. Error Recovery Workflow

```mermaid
flowchart TD
    APICall[API Call Made] --> Success{Successful?}
    Success -->|Yes| UpdateUI[Update UI]
    Success -->|No| ErrorType{Error Type?}
    
    ErrorType -->|Network| RetryLogic[Retry with Backoff]
    ErrorType -->|Auth| RefreshAuth[Refresh Authentication]
    ErrorType -->|AI Provider| FallbackProvider[Try Fallback Provider]
    ErrorType -->|Validation| ShowUserError[Show User-Friendly Error]
    
    RetryLogic --> MaxRetries{Max Retries?}
    MaxRetries -->|No| APICall
    MaxRetries -->|Yes| ShowOfflineMode[Show Offline Mode]
    
    RefreshAuth --> AuthSuccess{Auth Success?}
    AuthSuccess -->|Yes| APICall
    AuthSuccess -->|No| ForceLogin[Force Re-login]
    
    FallbackProvider --> FallbackSuccess{Success?}
    FallbackSuccess -->|Yes| UpdateUI
    FallbackSuccess -->|No| ShowUserError
```

## API Response Formats

### Standard Response Structure
```typescript
interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: Record<string, any>;
  };
  metadata: {
    timestamp: number;
    requestId: string;
    version: string;
  };
}
```

### PRD Session Response
```typescript
interface PRDSessionResponse {
  _id: Id<"prdSessions">;
  userId: string;
  projectName: string;
  targetAudience?: string;
  status: "active" | "paused" | "completed" | "abandoned";
  currentStep: string;
  questionnaire: Record<string, any>;
  generatedPRD?: {
    content: string;
    format: "markdown" | "json";
    generatedAt: number;
    version: string;
  };
  progress: {
    completedSteps: string[];
    totalSteps: number;
    percentComplete: number;
  };
  createdAt: number;
  updatedAt: number;
}
```

### Agent Message Response
```typescript
interface AgentMessageResponse {
  _id: Id<"messages">;
  sessionId: Id<"prdSessions">;
  senderId: string;
  senderType: "user" | "agent" | "system";
  content: string;
  messageType: "greeting" | "question" | "response" | "validation" | "completion";
  metadata: {
    agentPersonality?: string;
    workflowStep?: string;
    aiProvider?: string;
    confidence?: number;
  };
  timestamp: number;
}
```

## Real-time Subscriptions

### Session Updates
```typescript
// Client subscribes to session changes
const session = useQuery(api.prd.getSession, { sessionId });

// Automatically receives updates when:
// - Questionnaire progresses
// - Agent generates new messages
// - PRD generation completes
// - Session status changes
```

### Live Conversation
```typescript
// Client subscribes to conversation updates
const messages = useQuery(api.agents.getConversation, { sessionId });

// Real-time updates for:
// - New user messages
// - Agent responses
// - System notifications
// - Workflow transitions
```

## Error Handling Patterns

### Client-Side Error Handling
```typescript
const createPRDSession = useMutation(api.prd.createSession);

const handleCreateSession = async (projectData: ProjectData) => {
  try {
    const sessionId = await createPRDSession(projectData);
    router.push(`/prd/${sessionId}`);
  } catch (error) {
    if (error.code === "AUTHENTICATION_REQUIRED") {
      router.push("/login");
    } else if (error.code === "RATE_LIMIT_EXCEEDED") {
      showErrorToast("Too many requests. Please try again later.");
    } else {
      showErrorToast("Failed to create session. Please try again.");
    }
  }
};
```

### Server-Side Error Patterns
```typescript
export const secureFunction = mutation({
  args: { /* ... */ },
  handler: async (ctx, args) => {
    try {
      // Validate authentication
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        throw new ConvexError({
          code: "AUTHENTICATION_REQUIRED",
          message: "User must be authenticated",
        });
      }

      // Validate input
      const validatedArgs = validateInput(args);
      
      // Execute business logic
      const result = await performOperation(ctx, validatedArgs);
      
      return result;
    } catch (error) {
      // Log error for monitoring
      console.error("Function error:", error);
      
      // Re-throw with appropriate error code
      throw new ConvexError({
        code: error.code || "INTERNAL_ERROR",
        message: error.message || "An unexpected error occurred",
      });
    }
  }
});
```

## Performance Optimizations

### Query Optimization
- **Selective Queries** - Only fetch required fields
- **Index Usage** - Leverage Convex indexes for fast lookups
- **Pagination** - Implement cursor-based pagination for large datasets

### Real-time Update Optimization
- **Granular Subscriptions** - Subscribe only to relevant data changes
- **Optimistic Updates** - Update UI immediately with rollback capability
- **Connection Management** - Efficient WebSocket connection pooling