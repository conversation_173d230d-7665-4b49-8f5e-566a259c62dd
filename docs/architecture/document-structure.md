# Document Structure

This architecture document has been organized into focused domains for better maintainability:

- **[Technology Stack](./tech-stack.md)** - Complete technology stack, frameworks, and architectural decisions
- **[System Overview](./architecture-overview.md)** - High-level architecture, system design, and component interactions
- **[API & Workflows](./architecture-api.md)** - API specification and core application workflows
- **[Data Model](./architecture-data.md)** - Database schema and data structure design
- **[Deployment](./architecture-deployment.md)** - Project structure, development workflow, and deployment strategy

---

With these documents saved, the planning and architecture phase is complete. Your next step remains to engage the **<PERSON>rum Master (`@sm`)** to begin drafting the first development story.