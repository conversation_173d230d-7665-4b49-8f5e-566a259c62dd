# Technology Stack

## Core Framework Selection

### Frontend Framework
- **Next.js 15.4.1** - React-based full-stack framework
  - Server-side rendering (SSR) capabilities
  - Built-in routing and API routes
  - Turbo mode support for faster development
  - TypeScript first-class support

### Backend & Database
- **Convex** - Real-time backend-as-a-service
  - Real-time database with reactive queries
  - Built-in authentication (@convex-dev/auth)
  - Serverless functions
  - Type-safe API layer
  - Native workflow support (@convex-dev/workflow)
  - Agent framework integration (@convex-dev/agent)

### Language & Type Safety
- **TypeScript 5.x** - Primary development language
- **Zod 3.25.76** - Runtime type validation and schema definition
- **React 19.1.0** - UI framework with latest concurrent features

## AI & Agent Infrastructure

### AI Providers
- **AI SDK (@ai-sdk)** - Unified AI provider interface
  - Anthropic Claude integration (@ai-sdk/anthropic)
  - OpenAI integration (@ai-sdk/openai)
- **Convex Agent Framework (@convex-dev/agent)** - For "TheGeneral" agent implementation

### Agent Capabilities
- **@convex-dev/workflow** - Multi-step agent workflows
- **@convex-dev/rate-limiter** - Cost management and abuse prevention

## Styling & UI

### CSS Framework
- **Tailwind CSS 4.x** - Utility-first CSS framework
- **@tailwindcss/typography** - Enhanced typography support
- **@tailwindcss/postcss** - PostCSS integration

### Component Architecture
- **class-variance-authority** - Type-safe component variants
- **clsx & tailwind-merge** - Conditional styling utilities

## Development & Testing

### Testing Framework
- **Vitest 3.2.4** - Fast unit testing framework
  - JSdom environment for React testing
  - Coverage reports (@vitest/coverage-v8)
  - UI testing interface (@vitest/ui)
- **@testing-library/react** - React component testing utilities
- **Playwright** - End-to-end testing
  - @playwright/mcp integration for MCP testing

### Code Quality
- **Biome 2.1.2** - Fast linter and formatter (alternative to ESLint/Prettier)
- **ESLint 9.x** - Additional linting for Next.js specific rules
- **TypeScript strict mode** - Enhanced type checking

### Development Tools
- **Convex Test (@convex-test)** - Convex-specific testing utilities
- **MSW (Mock Service Worker)** - API mocking for tests
- **Vite** - Build tool and development server for testing
- **Task Master AI** - Project management and development workflow

## Authentication & Security

### Authentication
- **@auth/core** - Authentication framework
- **@convex-dev/auth** - Convex-native authentication integration

### Security Features
- Rate limiting via @convex-dev/rate-limiter
- Type-safe API boundaries with Zod validation
- Server-side rendering for reduced client-side attack surface

## Development Environment

### Package Management
- **pnpm** - Fast, efficient package manager (as specified in CLAUDE.md)

### Build & Deployment
- **Next.js build system** - Optimized production builds
- **Turbopack** - Fast development bundler
- **Convex deployment** - Integrated backend deployment

## Architectural Decisions

### Why This Stack?

1. **Next.js + Convex Mandate** - Specified in PRD requirements (NFR1)
2. **Real-time Capabilities** - Convex provides reactive data for interactive agent experiences
3. **Type Safety** - Full-stack TypeScript with Zod validation ensures reliability
4. **AI-First Design** - AI SDK + Convex Agents enable sophisticated "TheGeneral" personality
5. **Developer Experience** - Fast testing with Vitest, comprehensive tooling
6. **Cost Effectiveness** - Serverless architecture aligns with NFR3 cost requirements

### Technology Trade-offs

**Advantages:**
- Unified full-stack development experience
- Real-time data synchronization out of the box
- Strong type safety across the entire stack
- Excellent developer tooling and fast iteration cycles
- Built-in scalability with serverless architecture

**Considerations:**
- Learning curve for Convex-specific patterns
- Vendor lock-in to Convex ecosystem
- Less flexibility compared to traditional database solutions
- Newer ecosystem with evolving best practices

## Version Management Strategy

- **Semantic Versioning** - All dependencies use semantic versioning
- **Lock File Management** - pnpm-lock.yaml ensures reproducible builds
- **Regular Updates** - Next.js and React on latest stable versions
- **Security Updates** - Regular dependency audits and updates