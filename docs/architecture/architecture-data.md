# Data Model & Database Schema

## Database Technology

PRDGeneral uses **Convex** as its primary database, which provides:
- Document-based storage with flexible schemas
- Real-time reactive queries
- Built-in authentication integration
- Type-safe database operations
- Serverless scaling

## Core Data Entities

### 1. Users (Authentication)

Managed by Convex Auth system. User data is automatically handled through the authentication layer.

```typescript
// Implicit user data structure (managed by Convex Auth)
interface User {
  _id: Id<"users">;
  email: string;
  name?: string;
  image?: string;
  emailVerified?: number;
  // OAuth provider fields
  [key: string]: any;
}
```

### 2. PRD Sessions

**Table: `prdSessions`**

```typescript
import { defineTable } from "convex/server";
import { v } from "convex/values";

export const prdSessions = defineTable({
  // Core session data
  userId: v.string(), // References auth user
  projectName: v.string(),
  targetAudience: v.optional(v.string()),
  
  // Session state
  status: v.union(
    v.literal("active"),
    v.literal("paused"), 
    v.literal("completed"),
    v.literal("abandoned")
  ),
  currentStep: v.string(),
  
  // Questionnaire responses
  questionnaire: v.record(v.string(), v.any()),
  
  // Generated PRD output
  generatedPRD: v.optional(v.object({
    content: v.string(),
    format: v.union(v.literal("markdown"), v.literal("json")),
    generatedAt: v.number(),
    version: v.string(),
    wordCount: v.optional(v.number()),
    sections: v.optional(v.array(v.string())),
  })),
  
  // Progress tracking
  progress: v.object({
    completedSteps: v.array(v.string()),
    totalSteps: v.number(),
    percentComplete: v.number(),
  }),
  
  // Metadata
  createdAt: v.number(),
  updatedAt: v.number(),
  completedAt: v.optional(v.number()),
})
.index("by_user", ["userId"])
.index("by_status", ["status"])
.index("by_created", ["createdAt"]);
```

### 3. AI Agents

**Table: `agents`**

```typescript
export const agents = defineTable({
  // Session association
  sessionId: v.id("prdSessions"),
  
  // Agent configuration
  type: v.string(), // "TheGeneral"
  personality: v.string(), // "drill_instructor"
  status: v.union(
    v.literal("active"),
    v.literal("paused"),
    v.literal("completed"),
    v.literal("error")
  ),
  
  // Workflow state
  currentWorkflow: v.string(), // "prd_questionnaire"
  workflowStep: v.optional(v.string()),
  
  // Agent context and memory
  context: v.record(v.string(), v.any()),
  memory: v.optional(v.object({
    userPreferences: v.record(v.string(), v.any()),
    conversationSummary: v.optional(v.string()),
    motivationLevel: v.string(),
    responseStyle: v.string(),
  })),
  
  // AI provider settings
  aiProvider: v.optional(v.string()),
  modelSettings: v.optional(v.object({
    temperature: v.number(),
    maxTokens: v.number(),
    topP: v.optional(v.number()),
  })),
  
  // Performance metrics
  metrics: v.optional(v.object({
    messagesGenerated: v.number(),
    avgResponseTime: v.number(),
    successRate: v.number(),
    tokensUsed: v.number(),
  })),
  
  // Timestamps
  createdAt: v.number(),
  lastActiveAt: v.number(),
})
.index("by_session", ["sessionId"])
.index("by_status", ["status"])
.index("by_type", ["type"]);
```

### 4. Messages & Conversation

**Table: `messages`**

```typescript
export const messages = defineTable({
  // Message association
  sessionId: v.id("prdSessions"),
  
  // Sender information
  senderId: v.string(), // User ID or Agent ID
  senderType: v.union(
    v.literal("user"),
    v.literal("agent"), 
    v.literal("system")
  ),
  
  // Message content
  content: v.string(),
  messageType: v.union(
    v.literal("greeting"),
    v.literal("question"),
    v.literal("response"),
    v.literal("validation"),
    v.literal("completion"),
    v.literal("error"),
    v.literal("system_notification")
  ),
  
  // Message metadata
  metadata: v.optional(v.object({
    agentPersonality: v.optional(v.string()),
    workflowStep: v.optional(v.string()),
    aiProvider: v.optional(v.string()),
    confidence: v.optional(v.number()),
    tokensUsed: v.optional(v.number()),
    responseTime: v.optional(v.number()),
  })),
  
  // Threading and references
  replyToId: v.optional(v.id("messages")),
  threadId: v.optional(v.string()),
  
  // Status and visibility
  status: v.optional(v.union(
    v.literal("sent"),
    v.literal("delivered"),
    v.literal("read"),
    v.literal("hidden")
  )),
  
  // Timestamp
  timestamp: v.number(),
})
.index("by_session", ["sessionId"])
.index("by_timestamp", ["timestamp"])
.index("by_sender", ["senderId"])
.index("by_session_timestamp", ["sessionId", "timestamp"]);
```

### 5. Workflows & Steps

**Table: `workflows`**

```typescript
export const workflows = defineTable({
  // Workflow identification
  name: v.string(), // "prd_questionnaire"
  version: v.string(),
  
  // Workflow definition
  definition: v.object({
    steps: v.array(v.object({
      id: v.string(),
      name: v.string(),
      type: v.string(), // "question", "validation", "generation"
      config: v.record(v.string(), v.any()),
      nextSteps: v.array(v.string()),
      requiredInputs: v.optional(v.array(v.string())),
    })),
    entryPoint: v.string(),
    exitPoints: v.array(v.string()),
  }),
  
  // Workflow metadata
  description: v.string(),
  category: v.string(), // "prd_creation", "onboarding"
  isActive: v.boolean(),
  
  // Usage statistics
  usageStats: v.optional(v.object({
    totalExecutions: v.number(),
    successfulCompletions: v.number(),
    averageCompletionTime: v.number(),
    lastUsed: v.number(),
  })),
  
  // Timestamps
  createdAt: v.number(),
  updatedAt: v.number(),
})
.index("by_name", ["name"])
.index("by_active", ["isActive"])
.index("by_category", ["category"]);
```

### 6. Workflow Executions

**Table: `workflowExecutions`**

```typescript
export const workflowExecutions = defineTable({
  // Execution context
  sessionId: v.id("prdSessions"),
  agentId: v.id("agents"),
  workflowId: v.id("workflows"),
  
  // Current state
  currentStep: v.string(),
  status: v.union(
    v.literal("running"),
    v.literal("paused"),
    v.literal("completed"),
    v.literal("failed"),
    v.literal("cancelled")
  ),
  
  // Execution data
  executionData: v.record(v.string(), v.any()),
  stepHistory: v.array(v.object({
    stepId: v.string(),
    enteredAt: v.number(),
    exitedAt: v.optional(v.number()),
    result: v.optional(v.any()),
    error: v.optional(v.string()),
  })),
  
  // Progress tracking
  progress: v.object({
    completedSteps: v.number(),
    totalSteps: v.number(),
    percentComplete: v.number(),
  }),
  
  // Error handling
  errors: v.optional(v.array(v.object({
    stepId: v.string(),
    errorCode: v.string(),
    message: v.string(),
    timestamp: v.number(),
    recovered: v.boolean(),
  }))),
  
  // Timestamps
  startedAt: v.number(),
  completedAt: v.optional(v.number()),
  lastUpdatedAt: v.number(),
})
.index("by_session", ["sessionId"])
.index("by_agent", ["agentId"])
.index("by_status", ["status"])
.index("by_workflow", ["workflowId"]);
```

### 7. Rate Limiting & Usage Tracking

**Table: `rateLimits`**

```typescript
export const rateLimits = defineTable({
  // Rate limit identification
  identifier: v.string(), // user_id, ip_address, api_key
  type: v.string(), // "ai_generation", "session_creation", "message_sending"
  
  // Limit tracking
  count: v.number(),
  windowStart: v.number(),
  windowSize: v.number(), // in milliseconds
  limit: v.number(),
  
  // Status
  isBlocked: v.boolean(),
  blockedUntil: v.optional(v.number()),
  
  // Metadata
  lastRequest: v.number(),
  createdAt: v.number(),
})
.index("by_identifier_type", ["identifier", "type"])
.index("by_window_start", ["windowStart"])
.index("by_blocked", ["isBlocked"]);
```

### 8. AI Provider Usage & Costs

**Table: `aiUsage`**

```typescript
export const aiUsage = defineTable({
  // Usage context
  sessionId: v.optional(v.id("prdSessions")),
  agentId: v.optional(v.id("agents")),
  userId: v.string(),
  
  // Provider information
  provider: v.string(), // "anthropic", "openai"
  model: v.string(), // "claude-3", "gpt-4"
  
  // Usage metrics
  tokensUsed: v.object({
    input: v.number(),
    output: v.number(),
    total: v.number(),
  }),
  
  // Cost tracking
  estimatedCost: v.number(), // in cents
  actualCost: v.optional(v.number()),
  
  // Request details
  requestType: v.string(), // "chat", "completion", "embedding"
  responseTime: v.number(), // in milliseconds
  success: v.boolean(),
  errorCode: v.optional(v.string()),
  
  // Timestamp
  timestamp: v.number(),
})
.index("by_user", ["userId"])
.index("by_session", ["sessionId"])
.index("by_provider", ["provider"])
.index("by_timestamp", ["timestamp"])
.index("by_user_timestamp", ["userId", "timestamp"]);
```

## Database Relationships

```mermaid
erDiagram
    USERS ||--o{ PRD_SESSIONS : creates
    PRD_SESSIONS ||--|| AGENTS : has
    PRD_SESSIONS ||--o{ MESSAGES : contains
    AGENTS ||--o{ MESSAGES : generates
    AGENTS ||--o{ WORKFLOW_EXECUTIONS : executes
    WORKFLOWS ||--o{ WORKFLOW_EXECUTIONS : defines
    USERS ||--o{ RATE_LIMITS : tracked_by
    PRD_SESSIONS ||--o{ AI_USAGE : generates
    AGENTS ||--o{ AI_USAGE : uses

    USERS {
        string _id PK
        string email
        string name
        string image
    }
    
    PRD_SESSIONS {
        id _id PK
        string userId FK
        string projectName
        string status
        object questionnaire
        object generatedPRD
        number createdAt
    }
    
    AGENTS {
        id _id PK
        id sessionId FK
        string type
        string personality
        string status
        object context
        number createdAt
    }
    
    MESSAGES {
        id _id PK
        id sessionId FK
        string senderId
        string senderType
        string content
        string messageType
        number timestamp
    }
    
    WORKFLOW_EXECUTIONS {
        id _id PK
        id sessionId FK
        id agentId FK
        id workflowId FK
        string currentStep
        string status
        object executionData
    }
    
    WORKFLOWS {
        id _id PK
        string name
        string version
        object definition
        boolean isActive
    }
```

## Data Access Patterns

### 1. User Session Queries

```typescript
// Get all sessions for a user
const userSessions = await ctx.db
  .query("prdSessions")
  .withIndex("by_user", (q) => q.eq("userId", userId))
  .order("desc")
  .collect();

// Get active session
const activeSession = await ctx.db
  .query("prdSessions")
  .withIndex("by_user", (q) => q.eq("userId", userId))
  .filter((q) => q.eq(q.field("status"), "active"))
  .first();
```

### 2. Conversation Retrieval

```typescript
// Get all messages for a session
const conversation = await ctx.db
  .query("messages")
  .withIndex("by_session_timestamp", (q) => q.eq("sessionId", sessionId))
  .order("asc")
  .collect();

// Get recent messages with pagination
const recentMessages = await ctx.db
  .query("messages")
  .withIndex("by_session_timestamp", (q) => q.eq("sessionId", sessionId))
  .order("desc")
  .take(50);
```

### 3. Agent State Management

```typescript
// Get agent for session
const agent = await ctx.db
  .query("agents")
  .withIndex("by_session", (q) => q.eq("sessionId", sessionId))
  .first();

// Update agent context
await ctx.db.patch(agentId, {
  context: { ...existingContext, newData },
  lastActiveAt: Date.now(),
});
```

## Data Validation & Constraints

### Zod Schemas for Validation

```typescript
import { z } from "zod";

// PRD Session validation
export const prdSessionSchema = z.object({
  projectName: z.string().min(1).max(100),
  targetAudience: z.string().optional(),
  questionnaire: z.record(z.any()),
  status: z.enum(["active", "paused", "completed", "abandoned"]),
});

// Message validation
export const messageSchema = z.object({
  content: z.string().min(1).max(10000),
  messageType: z.enum([
    "greeting", "question", "response", 
    "validation", "completion", "error"
  ]),
  senderType: z.enum(["user", "agent", "system"]),
});

// Workflow step validation
export const workflowStepSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.string(),
  config: z.record(z.any()),
  nextSteps: z.array(z.string()),
});
```

## Performance Optimizations

### 1. Index Strategy

```typescript
// Optimized indexes for common queries
export default defineSchema({
  prdSessions: defineTable({...})
    .index("by_user", ["userId"])                    // User's sessions
    .index("by_status", ["status"])                  // Active sessions
    .index("by_created", ["createdAt"])              // Recent sessions
    .index("by_user_status", ["userId", "status"]),  // User's active sessions

  messages: defineTable({...})
    .index("by_session", ["sessionId"])              // Session messages
    .index("by_session_timestamp", ["sessionId", "timestamp"]), // Ordered messages
    .index("by_sender", ["senderId"]),               // Sender's messages
});
```

### 2. Data Archival Strategy

```typescript
// Automatic cleanup of old data
export const cleanupOldSessions = internalMutation({
  handler: async (ctx) => {
    const cutoffDate = Date.now() - (90 * 24 * 60 * 60 * 1000); // 90 days ago
    
    const oldSessions = await ctx.db
      .query("prdSessions")
      .withIndex("by_created")
      .filter((q) => q.lt(q.field("createdAt"), cutoffDate))
      .filter((q) => q.eq(q.field("status"), "abandoned"))
      .collect();

    for (const session of oldSessions) {
      // Archive or delete old sessions and related data
      await archiveSession(ctx, session._id);
    }
  },
});
```

### 3. Query Optimization

```typescript
// Efficient pagination
export const getMessagesPage = query({
  args: { 
    sessionId: v.id("prdSessions"),
    cursor: v.optional(v.string()),
    limit: v.number(),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("messages")
      .withIndex("by_session_timestamp", (q) => 
        q.eq("sessionId", args.sessionId)
      );

    if (args.cursor) {
      query = query.filter((q) => 
        q.gt(q.field("timestamp"), parseInt(args.cursor))
      );
    }

    const messages = await query
      .order("asc")
      .take(args.limit);

    return {
      messages,
      nextCursor: messages.length > 0 
        ? messages[messages.length - 1].timestamp.toString()
        : null,
    };
  },
});
```

## Data Security & Privacy

### 1. Row-Level Security

```typescript
// Users can only access their own data
export const getUserSessions = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return [];

    return await ctx.db
      .query("prdSessions")
      .withIndex("by_user", (q) => q.eq("userId", identity.subject))
      .collect();
  },
});
```

### 2. Data Sanitization

```typescript
// Clean sensitive data before storage
export const sanitizeMessage = (content: string): string => {
  // Remove potential PII, API keys, etc.
  return content
    .replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[EMAIL]')
    .replace(/\b(?:sk-|pk_)[A-Za-z0-9]{20,}\b/g, '[API_KEY]')
    .replace(/\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/g, '[CARD_NUMBER]');
};
```

### 3. Audit Logging

```typescript
// Track data access and modifications
export const auditLogs = defineTable({
  userId: v.string(),
  action: v.string(), // "create", "read", "update", "delete"
  resourceType: v.string(), // "prdSession", "message", "agent"
  resourceId: v.string(),
  metadata: v.optional(v.record(v.string(), v.any())),
  timestamp: v.number(),
  ipAddress: v.optional(v.string()),
  userAgent: v.optional(v.string()),
})
.index("by_user", ["userId"])
.index("by_timestamp", ["timestamp"])
.index("by_resource", ["resourceType", "resourceId"]);
```