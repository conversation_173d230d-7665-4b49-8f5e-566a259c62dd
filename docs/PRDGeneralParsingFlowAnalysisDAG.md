# PRDGeneral Parsing Flow Analysis - DAG/Workflow Requirements

## 1. Main Hierarchical Cascade Flow

```mermaid
flowchart TD
    A[User Input] --> B{Vagueness Gate}

    B -->|FAIL| C[Immediate Rejection Response]
    B -->|PASS| D{Focus Gate<br/>Single vs Multi-Product}

    D -->|FAIL<br/>Multi-Product| E[Binary Choice Forcing Response]
    D -->|PASS| F{Scope Gate<br/>Feature Analysis}

    F -->|FAIL<br/>Kitchen-Sink| G[Scope Creep Rejection Response]
    F -->|PASS| H[Refinement Loop Entry]

    H --> I{Specificity Check}
    I -->|Needs More Detail| J[Clarification Round N]
    I -->|Sufficient Detail| K[Definition Lock Response]

    J --> L[Wait for User Response]
    L --> M[User Response] --> I

    C --> N[Response Generator]
    E --> N
    G --> N
    K --> N

    N --> O[Final Response to User]

    style A fill:#ffffff, color:#000000
    style B fill:#ff9999, color:#000000
    style C fill:#ffffff, color:#000000
    style D fill:#ffcc99, color:#000000
    style E fill:#ffffff, color:#000000
    style F fill:#99ccff, color:#000000
    style G fill:#ffffff, color:#000000
    style H fill:#99ff99, color:#000000
    style I fill:#ffffff, color:#000000
    style J fill:#ffffff, color:#000000
    style K fill:#ffffff, color:#000000
    style L fill:#ffffff, color:#000000
    style M fill:#ffffff, color:#000000
    style N fill:#cc99ff, color:#000000
    style O fill:#ffffff, color:#000000
```

## 2. Parsing Pattern State Machine

```mermaid
stateDiagram-v2
    [*] --> Analyzing

    Analyzing --> VaguenessCheck : Parse Input

    VaguenessCheck --> Rejected : Technical Jargon Detected
    VaguenessCheck --> FocusCheck : Clear Language Found

    FocusCheck --> ChoiceForcing : Multiple Products Detected
    FocusCheck --> ScopeCheck : Single Product Confirmed

    ScopeCheck --> ScopeRejection : Feature List > 5 Items
    ScopeCheck --> Refinement : Focused Feature Set

    Refinement --> ClarificationRound : Needs More Detail
    Refinement --> DefinitionLock : Sufficient Specificity

    ClarificationRound --> WaitingForUser : Question Generated
    WaitingForUser --> Refinement : User Responds

    Rejected --> [*] : Response Sent
    ChoiceForcing --> [*] : Response Sent
    ScopeRejection --> [*] : Response Sent
    DefinitionLock --> [*] : Response Sent

    note right of VaguenessCheck
        "Too vague and jargon-heavy"
        "No architecture details yet"
    end note

    note right of FocusCheck
        "Do one thing well"
        "Pick ONE product"
    end note

    note right of ScopeCheck
        "11+ major feature categories"
        "Kitchen-sink approach"
    end note
```

## 3. Data Flow and Parser Dependencies

```mermaid
flowchart LR
    subgraph Input Processing
        A[Raw User Input] --> B[Text Normalization]
        B --> C[Keyword Extraction]
        B --> D[Structure Analysis]
    end

    subgraph Parser Layer
        C --> E[Vagueness Parser]
        D --> F[Multi-Product Parser]
        C --> G[Scope Parser]
        D --> G
        C --> H[Specificity Parser]
    end

    subgraph Decision Engine
        E --> I{Gate 1: Vagueness}
        F --> J{Gate 2: Focus}
        G --> K{Gate 3: Scope}
        H --> L{Gate 4: Specificity}
    end

    subgraph Response Generation
        I --> M[Rejection Generator]
        J --> N[Choice Forcing Generator]
        K --> O[Scope Limiting Generator]
        L --> P[Clarification Generator]
        L --> Q[Lock Definition Generator]
    end

    subgraph Output
        M --> R[Response Formatter]
        N --> R
        O --> R
        P --> R
        Q --> R
        R --> S[Final Response]
    end

    style A fill:#ffffff, color:#000000
    style B fill:#ffffff, color:#000000
    style C fill:#ffffff, color:#000000
    style D fill:#ffffff, color:#000000
    style E fill:#ff9999, color:#000000
    style F fill:#ffcc99, color:#000000
    style G fill:#99ccff, color:#000000
    style H fill:#99ff99, color:#000000
    style I fill:#ffffff, color:#000000
    style J fill:#ffffff, color:#000000
    style K fill:#ffffff, color:#000000
    style L fill:#ffffff, color:#000000
    style M fill:#ffffff, color:#000000
    style N fill:#ffffff, color:#000000
    style O fill:#ffffff, color:#000000
    style P fill:#ffffff, color:#000000
    style Q fill:#ffffff, color:#000000
    style R fill:#ffffff, color:#000000
    style S fill:#ffffff, color:#000000
```

## 4. Parser Pattern Matching Rules

```mermaid
flowchart TD
    subgraph Vagueness Patterns
        A1[Technical Jargon Without Context]
        A2[Solution-First Language]
        A3[Architecture Terms Without Problems]
        A1 --> A4[FAIL: Vagueness Gate]
        A2 --> A4
        A3 --> A4
    end

    subgraph Focus Patterns
        B1[Multiple Product Names]
        B2[AND/OR Connectors]
        B3[Platform + Tool Descriptions]
        B1 --> B4[FAIL: Focus Gate]
        B2 --> B4
        B3 --> B4
    end

    subgraph Scope Patterns
        C1[Feature Lists > 5 Items]
        C2[Multiple Core Functions]
        C3[Ecosystem Language]
        C1 --> C4[FAIL: Scope Gate]
        C2 --> C4
        C3 --> C4
    end

    subgraph Specificity Patterns
        D1[Complete Problem Definition]
        D2[Single Core Function]
        D3[Specific User Type]
        D4[Concrete Triggers/Actions]
        D1 --> D5[PASS: Ready for Lock]
        D2 --> D5
        D3 --> D5
        D4 --> D5
    end

    style A1 fill:#ffffff, color:#000000
    style A2 fill:#ffffff, color:#000000
    style A3 fill:#ffffff, color:#000000
    style A4 fill:#ffffff, color:#000000
    style B1 fill:#ffffff, color:#000000
    style B2 fill:#ffffff, color:#000000
    style B3 fill:#ffffff, color:#000000
    style B4 fill:#ffffff, color:#000000
    style C1 fill:#ffffff, color:#000000
    style C2 fill:#ffffff, color:#000000
    style C3 fill:#ffffff, color:#000000
    style C4 fill:#ffffff, color:#000000
    style D1 fill:#ffffff, color:#000000
    style D2 fill:#ffffff, color:#000000
    style D3 fill:#ffffff, color:#000000
    style D4 fill:#ffffff, color:#000000
    style D5 fill:#ffffff, color:#000000
```

## 5. Refinement Loop Detail

```mermaid
sequenceDiagram
    participant U as User
    participant P as Parser System
    participant G as GoalGetter

    U->>P: Input with insufficient detail
    P->>P: Passes Vagueness/Focus/Scope Gates
    P->>P: Fails Specificity Check
    P->>G: Generate Clarification Round N
    G->>U: Targeted Questions

    loop Clarification Rounds
        U->>P: Answers
        P->>P: Specificity Re-check
        alt Still Insufficient
            P->>G: Generate Round N+1
            G->>U: Deeper Questions
        else Sufficient Detail
            P->>G: Generate Definition Lock
            G->>U: "DEFINITION LOCKED"
        end
    end
```

## 6. DAG/Workflow Implementation Requirements

### Node Types Needed:
1. **Parser Nodes**: Vagueness, Focus, Scope, Specificity
2. **Gate Nodes**: Boolean decision points with fail-fast logic
3. **Generator Nodes**: Response generation based on failure type
4. **Loop Nodes**: Iterative refinement with state preservation
5. **Merger Nodes**: Combine parsing results for complex decisions

### Edge Types Needed:
1. **Pass/Fail Edges**: Binary outcomes from gates
2. **Loop Edges**: Refinement iteration cycles
3. **Short-Circuit Edges**: Direct paths to response generation
4. **Context Edges**: Carry parsing metadata between nodes

### State Requirements:
- **Round Counter**: Track clarification iteration
- **Parsing Results**: Cache results from earlier gates
- **User Context**: Maintain conversation history
- **Mode State**: Track current focus (clarification/generation/refinement)

### Performance Considerations:
- **Early Exit**: Implement true short-circuiting to avoid unnecessary parsing
- **Caching**: Store parser results to avoid re-analysis
- **Parallelization**: Run independent parsers concurrently where possible
- **Timeout Handling**: Prevent infinite refinement loops