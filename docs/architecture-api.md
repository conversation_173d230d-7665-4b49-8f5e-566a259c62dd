# PRDGeneral - API Specification & Core Workflows

## 4. API Specification & Core Workflows

The "API" is the set of query and mutation functions defined in the Convex backend. OAuth is handled by Convex's built-in authentication.

**Core Functions:**

- `createProject`
- `listProjects`
- `getProject`
- `sendMessageToGeneral`
- `getChatHistory` (real-time)

**Core Workflow:**

```mermaid
sequenceDiagram
    participant User
    participant Frontend (Next.js)
    participant Backend (Convex)
    participant TheGeneral (Agent Logic)

    User->>Frontend: Enters message
    Frontend->>Backend: Calls sendMessageToGeneral(message)
    Backend->>TheGeneral: Invokes agent with message
    TheGeneral-->>Backend: Processes input, updates state in DB
    Backend-->>Frontend: Returns agent's response
```