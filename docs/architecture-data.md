# PRDGeneral - Data Model

## 5. Database Schema (`convex/schema.ts`)

```typescript
import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  users: defineTable({
    name: v.string(),
    tokenIdentifier: v.string(),
  }).index("by_token", ["tokenIdentifier"]),

  projects: defineTable({
    userId: v.id("users"),
    name: v.string(),
    prdContent: v.optional(v.string()),
  }).index("by_user", ["userId"]),

  messages: defineTable({
    projectId: v.id("projects"),
    author: v.union(v.literal("user"), v.literal("TheGeneral")),
    text: v.string(),
  }).index("by_project", ["projectId"]),
});
```