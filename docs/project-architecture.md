# PRDGeneral Fullstack Architecture Document

| Date       | Version | Description                       | Author  |
| :--------- | :------ | :-------------------------------- | :------ |
| 2025-08-01 | 1.0     | Initial architectural draft creation. | Winston |

## 1. Introduction

This document outlines the complete fullstack architecture for **PRDGeneral**, including backend systems, frontend implementation, and their integration. It serves as the single source of truth for AI-driven development.

The project is being built from a custom structure, not a starter template.

## 2. High-Level Architecture

### Technical Summary
The architecture for `PRDGeneral` will be a modern, serverless web application. It will feature a **Next.js** frontend for a clean, minimalist user interface, paired with a **Convex** backend that will manage the database, serverless functions, and the core logic for "TheGeneral" agent. This approach prioritizes rapid development, scalability, and cost-effectiveness.

### Platform and Infrastructure Choice
* **Frontend Hosting:** The Next.js frontend will be deployed to **Vercel**. It offers seamless integration, automatic deployments, and a generous free tier perfect for an MVP.
* **Backend Infrastructure:** **Convex** will provide the complete backend infrastructure, including the database, serverless functions for agent logic, and real-time capabilities.

### Repository Structure
The project will use a **monorepo** structure to keep the Next.js frontend and Convex backend code together in a single Git repository, simplifying development.

### High-Level Architecture Diagram
```mermaid
graph TD
    User -->|Interacts with UI| NextJS[Next.js Frontend on Vercel]
    NextJS -->|Calls Functions & Subscribes to Data| Convex[Convex Backend]
    Convex -->|Real-time Updates| NextJS
    Convex -- manages --> DB[(Database)]
    Convex -- executes --> Agent[Agent Logic]
```

## 3. Tech Stack

This is the definitive list of technologies to be used for the MVP.

| Category             | Technology                    | Rationale                                                                        |
| :------------------- | :---------------------------- | :------------------------------------------------------------------------------- |
| Language             | **TypeScript** | Adds type safety to JavaScript, which is crucial for agent-written code.           |
| Frontend Framework   | **Next.js (React)** | A modern, performant framework for web applications.                               |
| Backend Platform     | **Convex** | Handles the database, serverless functions, and agent logic.                       |
| Styling              | **Tailwind CSS** | A utility-first CSS framework perfect for creating clean, minimalist designs quickly.|
| State Management     | **React Hooks + Convex Hooks**| A simple, powerful approach that avoids premature complexity.                      |
| Testing              | **Vitest & React Testing Library** | A modern, fast test runner compatible with Next.js and Convex backend functions.     |
| CI/CD                | **Vercel CI/CD** | Tightly integrated with Vercel hosting for seamless, automatic deployments.       |
| Dev Environment      | **Devbox** | Provides an isolated, reproducible development environment.                        |
| Package Manager      | **pnpm** (recommended)        | A fast, disk space-efficient package manager.                                    |

## 4. Data Models

The core data structure is composed of Users, Projects, and the PRD Content itself.

## 5. API Specification

The "API" is the set of query and mutation functions defined in our Convex backend that the Next.js frontend will call directly, providing end-to-end type safety.

* **User Management:** Handled by Convex's built-in OAuth providers.
* **Project Management:**
    * `createProject(name: string)`
    * `listProjects()`
    * `getProject(projectId: Id<"projects">)`
* **Agent Interaction:**
    * `sendMessageToGeneral(projectId: Id<"projects">, message: string)`
    * `getChatHistory(projectId: Id<"projects">)` (real-time query)

## 6. Components

The application is broken down into three primary logical components:

* **Frontend Application (Next.js):** Provides the user interface, handles user interactions, and communicates in real-time with the Convex backend.
* **Backend Services (Convex):** Manages the database, user authentication, and executes the core agent logic in serverless functions.
* **"TheGeneral" Agent (Logical Component):** The "brain" running on Convex, responsible for processing user input and generating the PRD content.

## 7. External APIs

There will be no external API integrations for the MVP to keep the project lean and focused.

## 8. Core Workflows

The primary workflow is the conversation between the user and "TheGeneral".

```mermaid
sequenceDiagram
    participant User
    participant Frontend (Next.js)
    participant Backend (Convex)
    participant TheGeneral (Agent Logic)

    User->>Frontend: Enters message
    Frontend->>Backend: Calls sendMessageToGeneral(message)
    Backend->>TheGeneral: Invokes agent with message
    TheGeneral-->>Backend: Processes input, updates state in DB
    Backend-->>Frontend: Returns agent's response
    Note right of Backend: Convex's real-time query updates the UI automatically
```

## 9. Database Schema

This schema will be defined in `convex/schema.ts`.

```typescript
// convex/schema.ts
import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  // Table to store user information
  users: defineTable({
    name: v.string(),
    // This will be the identifier from your Convex Auth/OAuth setup
    tokenIdentifier: v.string(),
  }).index("by_token", ["tokenIdentifier"]),

  // Table to store each PRD project
  projects: defineTable({
    userId: v.id("users"),
    name: v.string(),
    // We can store the generated PRD content here as a single string
    prdContent: v.optional(v.string()),
  }).index("by_user", ["userId"]),

  // Table to store the conversation history for each project
  messages: defineTable({
    projectId: v.id("projects"),
    author: v.union(v.literal("user"), v.literal("TheGeneral")),
    text: v.string(),
  }).index("by_project", ["projectId"]),
});
```

## 10. Frontend Architecture

* **Component Structure:** Components will be located in `src/components/`, separated into generic `ui/` components and more complex `shared/` components.
* **State Management:** Local state will use React Hooks (`useState`). Global and server state will be managed by Convex's real-time query hooks (`useQuery`).
* **Routing:** The Next.js App Router (`src/app/`) will be used, with routes for `/`, `/dashboard`, and `/project/[projectId]`.
* **Services Layer:** All backend communication will be handled via the Convex client.

## 11. Backend Architecture

* **Service Architecture:** A serverless function model hosted on Convex. All backend logic will reside in TypeScript files within the `convex/` directory.
* **Data Access Layer:** We will use Convex's built-in, type-safe database client (`ctx.db`) directly within functions.
* **Authentication:** Handled by Convex's built-in OAuth, configured in `convex/auth.config.ts`. Authorization will be enforced in every function.

## 12. Unified Project Structure

```plaintext
/prd-general-monorepo
├── convex/                  # All backend code lives here
│   ├── schema.ts            # The database schema
│   ├── auth.config.ts       # Authentication configuration
│   └── messages.ts          # Backend functions for the agent/chat
├── src/                     # All frontend code (Next.js)
│   ├── app/                 # Next.js App Router for pages
│   ├── components/            # Reusable React components
│   └── lib/                   # Utility functions for the frontend
├── public/                  # Static assets (images, fonts, helmet icon)
├── docs/                    # Project documentation
├── .env.local               # Frontend environment variables
├── package.json             # Project dependencies and scripts
└── tsconfig.json            # TypeScript configuration
```

## 13. Development Workflow

**Prerequisites:** Node.js, Devbox, pnpm (recommended) or npm.

**Setup Steps:**
1.  Clone the repository.
2.  Start the isolated environment: `devbox shell`
3.  Install dependencies: `pnpm install`
4.  Run Convex backend: `npx convex dev`
5.  Run Next.js frontend: `pnpm run dev`

**Environment Configuration:**
Create a `.env.local` file with the `NEXT_PUBLIC_CONVEX_URL` provided by the `convex dev` command.

## 14. Deployment Architecture

* **Frontend (Next.js):** Deployed to **Vercel**. Pushing to the `main` branch automatically triggers a production deployment.
* **Backend (Convex):** Deployed via the Convex CLI (`npx convex deploy`).
* **CI/CD Pipeline:** We will use **Vercel's built-in CI/CD** for the frontend.
* **Environments:** Production (`main` branch), Preview (pull requests), and Development (local).

## 15. Security and Performance

* **Security:** Authentication is handled by Convex OAuth. Authorization is enforced in every backend function to ensure users only access their own data.
* **Performance:** We will leverage Next.js's built-in optimizations (code-splitting, static generation) and Vercel's Edge Network (CDN). Convex's architecture and our indexed database schema will ensure a fast backend.

## 16. Testing Strategy

* **Backend Testing (Convex):** **Vitest** will be used to test Convex functions in an isolated environment. Tests will live in `convex/tests/`.
* **Frontend Testing (Next.js):** **Vitest** and **React Testing Library** will be used for component testing. Tests will be co-located with components.
* **E2E Testing:** Out of scope for the MVP.

## 17. Coding Standards, Error Handling, & Monitoring

* **Coding Standards:** We will enforce a strict configuration of **ESLint** and **Prettier** to ensure code quality and consistency.
* **Error Handling:** The frontend will use React **Error Boundaries**. The backend will use `try...catch` blocks in Convex functions.
* **Monitoring:** We will use the built-in **Vercel Analytics** for the frontend and the **Convex Dashboard** for backend monitoring.