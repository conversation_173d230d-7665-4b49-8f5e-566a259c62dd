---
type: "agent_requested"
description: "Before begining work on any 1 task"
---
1. **Check for in-progress `task-master` task**

   * If found: identify current subtask.
   * If no subtask in progress: select next subtask.

2. **Validate current `task-master` subtask**

   * Confirm if still valid and actionable.
   * Skip or archive if obsolete.

3. **Report subtask status to `task-master`**

   * Update subtask state: `complete`, `blocked`, `in-progress`.
   * If all subtasks complete, mark parent task complete.

4. **Select next `task-master` subtask**

   * Choose next high-priority subtask not yet started.

5. **Create Git resources**

   * **GitHub Issue Management:**
     - If parent task has existing GitHub issue: Add subtask as checklist items within that issue
     - If no GitHub issue exists for parent task: Create new GitHub issue for the parent task with subtasks as checklist items
     - Use GitHub's native task list format with `- [ ]` checkboxes for subtasks
     - Include detailed implementation requirements for each subtask checkbox
   * Create a Git branch named after subtask number and slug (e.g. `3.2-add-login-form`).

6. **Generate `augment` tasks**

   * Break subtask into atomic `augment` tasks.
   * Populate augment task tracker with these.

7. **Begin execution**

   * Start with first `augment` task.
   * Keep GitHub Issue updated with progress.

8. **Testing**

  * Have the user confirm lint, build, test, test:e2e, and convex dev are passing

8. **Complete subtask**

   * Ensure all augment tasks are done.
   * Mark subtask as `complete` in `task-master` system **before final commit**.
   * Ask the user to run tests, linting, and e2e tests
   * Commit final changes, push branch.
   * Open PR to main/integration branch.
   * Link PR to GitHub Issue via “Closes #issue-number” in PR description.
