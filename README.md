# PRDGeneral

An AI-powered Product Requirements Document (PRD) generation system that transforms rough product ideas into crystal-clear, comprehensive PRDs through intelligent validation and refinement.

## 🚀 Features

- **Cascade Flow Validation**: 4-gate pipeline ensuring high-quality requirements
- **AI-Powered Analysis**: Intelligent pattern detection + LLM validation
- **Real-time Chat Interface**: Interactive requirement gathering and refinement
- **Multi-Provider AI**: Support for Claude, GPT-4, and Perplexity
- **Quality Assurance**: Automated validation for vagueness, focus, scope, and specificity

## 🏗️ Architecture

Built with modern technologies for scalability and performance:

- **Frontend**: Next.js 15 with React 19
- **Backend**: Convex with real-time synchronization
- **AI Integration**: Convex Agent framework with multiple LLM providers
- **Database**: Convex's built-in database with real-time queries
- **Authentication**: Convex Auth with multiple provider support

## 📖 Documentation

- [Architecture Overview](./docs/architecture.md) - Complete system architecture
- [Cascade Flow System](./docs/gates.md) - Detailed gate validation documentation
- [Convex Agents](./docs/convex/agents/README.md) - Agent system documentation

## 🚦 Getting Started

### Prerequisites

- Node.js 18+ 
- pnpm (recommended package manager)
- Convex account
- AI provider API keys (Anthropic Claude recommended)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd PRDGeneral
   ```

2. **Install dependencies**
   ```bash
   pnpm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   # Add your API keys:
   # ANTHROPIC_API_KEY=your_claude_key
   # OPENAI_API_KEY=your_openai_key (optional)
   # PERPLEXITY_API_KEY=your_perplexity_key (optional)
   ```

4. **Initialize Convex**
   ```bash
   npx convex dev
   # Follow the setup prompts to create/connect your Convex project
   ```

5. **Start the development server**
   ```bash
   pnpm dev
   ```

6. **Open your browser**
   ```
   http://localhost:3000
   ```

## 🧪 Testing

Run the comprehensive test suite:

```bash
# Unit and integration tests
pnpm test

# End-to-end tests
pnpm test:e2e

# Coverage report
pnpm test:coverage
```

## 🔄 Cascade Flow System

The heart of PRDGeneral is its 4-gate validation pipeline:

1. **Vagueness Gate** - Eliminates unclear, ambiguous language
2. **Focus Gate** - Ensures single product focus
3. **Scope Gate** - Validates appropriate MVP/MLP scope
4. **Specificity Gate** - Ensures actionable, detailed requirements

Each gate uses hybrid analysis combining fast pattern detection with AI-powered validation for optimal accuracy and performance.

## 🤖 AI Integration

### Supported Providers
- **Claude 3.5 Sonnet** (Primary) - Best for requirement analysis
- **GPT-4** (Secondary) - Fallback and comparison
- **Perplexity** (Research) - Enhanced context gathering

### Agent System
Built on Convex Agent framework for:
- Persistent conversation threads
- Context-aware interactions
- Reliable state management
- Scalable AI operations

## 📊 Performance & Quality

- **Early Exit Pipeline**: Stops processing on first gate failure
- **Hybrid Analysis**: Pattern matching + AI for optimal speed/accuracy
- **Real-time Updates**: Instant UI feedback via Convex subscriptions
- **Error Recovery**: Graceful degradation and user-friendly error messages

## 🔒 Security & Privacy

- **Data Encryption**: All data encrypted at rest and in transit
- **Rate Limiting**: Per-user and anonymous user limits
- **Input Validation**: Comprehensive sanitization and validation
- **Secure Authentication**: Convex Auth with multiple providers

## 🛠️ Development

### Project Structure
```
├── src/                    # Next.js frontend
│   ├── components/         # React components
│   ├── hooks/             # Custom React hooks
│   └── types/             # TypeScript definitions
├── convex/                # Convex backend
│   ├── agents/            # AI agents for gate validation
│   ├── workflows/         # Legacy gate functions
│   └── flows/             # State management utilities
├── docs/                  # Documentation
├── e2e/                   # End-to-end tests
└── test-results/          # Test outputs
```

### Key Commands
```bash
pnpm dev              # Start development servers
pnpm build            # Build for production  
pnpm test             # Run tests
pnpm lint             # Lint code
pnpm typecheck        # Type checking
```

## 📈 Monitoring & Analytics

- **Performance Metrics**: Gate processing times and success rates
- **Quality Metrics**: User satisfaction and PRD quality correlation
- **Error Tracking**: Comprehensive error logging and monitoring
- **Usage Analytics**: User behavior and system performance insights

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Convex](https://www.convex.dev/) for the excellent backend platform
- [Anthropic](https://www.anthropic.com/) for Claude AI
- [Next.js](https://nextjs.org/) for the frontend framework
- Open source community for inspiration and tools
