"use client";

import { ConvexAuthProvider } from "@convex-dev/auth/react";
import { ConvexReactClient } from "convex/react";
import { ReactNode } from "react";

const convex = new ConvexReactClient(
	process.env.NEXT_PUBLIC_CONVEX_URL as string,
);

export default function ConvexProvider({ children }: { children: ReactNode }) {
	return <ConvexAuthProvider client={convex}>{children}</ConvexAuthProvider>;
}
