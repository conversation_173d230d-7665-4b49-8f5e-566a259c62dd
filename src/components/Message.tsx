import { Message as MessageType } from "ai";

interface MessageProps {
	message: MessageType;
}

export default function Message({ message }: MessageProps) {
	const isUser = message.role === "user";
	const timestamp = message.createdAt
		? new Date(message.createdAt).toLocaleTimeString([], {
				hour: "2-digit",
				minute: "2-digit",
			})
		: "";

	return (
		<div
			className={`flex ${isUser ? "justify-end" : "justify-start"}`}
			role="article"
			aria-label={`${isUser ? "User message" : "Assistant message"}${timestamp ? ` at ${timestamp}` : ""}`}
			data-message-id={message.id}
			data-testid={isUser ? "user-message" : "assistant-message"}
		>
			<div
				className={`max-w-[85%] sm:max-w-xs lg:max-w-md xl:max-w-lg px-3 sm:px-4 py-2 sm:py-3 rounded-lg ${
					isUser
						? "bg-blue-500 text-white rounded-br-sm"
						: "bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white rounded-bl-sm"
				}`}
				data-testid={
					isUser ? "user-message-content" : "assistant-message-content"
				}
			>
				<div className="whitespace-pre-wrap text-sm sm:text-base leading-relaxed">
					{message.content}
				</div>
				{timestamp && (
					<div
						className={`text-xs mt-1 opacity-70 ${
							isUser ? "text-blue-100" : "text-gray-500 dark:text-gray-400"
						}`}
						aria-label={`Message sent at ${timestamp}`}
					>
						{timestamp}
					</div>
				)}
			</div>
		</div>
	);
}
