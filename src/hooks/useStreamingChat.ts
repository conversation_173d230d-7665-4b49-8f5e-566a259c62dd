"use client";

import { useThreadMessages } from "@convex-dev/agent/react";
import { useMutation } from "convex/react";
import { useCallback, useRef, useState } from "react";
import { api } from "../../convex/_generated/api";
import type { Message } from "../types";

export interface StreamingChatState {
	messages: Message[];
	input: string;
	isLoading: boolean;
	isStreaming: boolean;
	isError: boolean;
	canCancel: boolean;
	error: string | null;
	currentStreamingMessage: Message | null;
	threadId: string | undefined;
}

export interface StreamingChatActions {
	handleInputChange: (
		e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
	) => void;
	handleSubmit: (e: React.FormEvent) => Promise<void>;
	cancelStream: () => Promise<void>;
	retry: () => Promise<void>;
}

export function useStreamingChat(initialThreadId?: string) {
	// Local state for thread management
	const [threadId, setThreadId] = useState<string | undefined>(initialThreadId);

	// Convex hooks using the workflow approach
	const startWorkflow = useMutation(
		api.workflows.cascadeWorkflow.startWorkflow,
	);
	const storedMessages = useThreadMessages(
		api.messages.listThreadMessages,
		threadId ? { threadId } : "skip",
		{ initialNumItems: 10 },
	);

	// Local state for real-time streaming
	const [input, setInput] = useState("");
	const [isStreaming, setIsStreaming] = useState(false);
	const [isError, setIsError] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [currentStreamingMessage, setCurrentStreamingMessage] =
		useState<Message | null>(null);

	// References for streaming control
	const streamingAbortController = useRef<AbortController | null>(null);

	// Use thread messages from the agent system
	const messages: Message[] = [
		...(storedMessages?.results || []).map((msg) => {
			// Convert agent message format to our Message type
			const role = msg.message?.role;
			if (role !== "user" && role !== "assistant") {
				console.warn(`Unexpected message role: ${role}`);
			}
			return {
				id: msg._id,
				role: role === "user" || role === "assistant" ? role : "assistant",
				content: msg.text || "",
				createdAt: new Date(msg._creationTime),
			};
		}),
		...(currentStreamingMessage && currentStreamingMessage.role === "assistant"
			? [currentStreamingMessage]
			: []),
	];

	const isLoading = isStreaming && !currentStreamingMessage;
	const canCancel = isStreaming && streamingAbortController.current !== null;

	// Handle input changes
	const handleInputChange = useCallback(
		(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
			setInput(e.target.value);
		},
		[],
	);

	// Cancel current stream
	const cancelStream = useCallback(async () => {
		if (streamingAbortController.current) {
			streamingAbortController.current.abort();
			setIsStreaming(false);
			setCurrentStreamingMessage(null);
			streamingAbortController.current = null;
		}
	}, []);

	// Submit message using agent approach
	const handleSubmit = useCallback(
		async (e: React.FormEvent) => {
			e.preventDefault();

			if (!input.trim() || isStreaming) return;

			const userMessage = input.trim();
			setInput("");
			setIsStreaming(true);
			setIsError(false);
			setError(null);

			try {
				// Create abort controller for cancellation
				const abortController = new AbortController();
				streamingAbortController.current = abortController;

				// Start the cascade workflow
				const result = await startWorkflow({
					userInput: userMessage,
					threadId: threadId,
				});

				// Update threadId if a new one was created
				if (result.threadId && result.threadId !== threadId) {
					setThreadId(result.threadId);
				}

				// Clear any streaming message state
				setCurrentStreamingMessage(null);
			} catch (err) {
				console.error("Submit error:", err);
				setIsError(true);

				// Guard against cascade flow errors and provide user-friendly messages
				let errorMessage = "Failed to send message";
				if (err instanceof Error) {
					// Check for specific cascade flow errors and provide better user messaging
					if (err.message.includes("gate") || err.message.includes("cascade")) {
						errorMessage = "Message processing failed. Please try again.";
					} else if (
						err.message.includes("thread") ||
						err.message.includes("agent")
					) {
						errorMessage =
							"Chat connection error. Please refresh and try again.";
					} else {
						errorMessage = err.message;
					}
				}

				setError(errorMessage);
				setCurrentStreamingMessage(null);
			} finally {
				setIsStreaming(false);
				streamingAbortController.current = null;
			}
		},
		[input, isStreaming, threadId, startWorkflow],
	);

	// Retry last message
	const retry = useCallback(async () => {
		if (isStreaming || !storedMessages?.results?.length) return;

		const lastMessage =
			storedMessages.results[storedMessages.results.length - 1];
		if (lastMessage.message?.role !== "user") return;

		const messageText = lastMessage.text;
		if (!messageText) {
			console.warn("Last message has no text content");
			return;
		}
		setInput(messageText);
		// The user can then submit again
	}, [isStreaming, storedMessages]);

	const state: StreamingChatState = {
		messages,
		input,
		isLoading,
		isStreaming,
		isError,
		canCancel,
		error,
		currentStreamingMessage,
		threadId,
	};

	const actions: StreamingChatActions = {
		handleInputChange,
		handleSubmit,
		cancelStream,
		retry,
	};

	return { ...state, ...actions };
}
