import {
	generateSecureSessionId,
	isValidSessionIdFormat,
} from "../sessionUtils";

describe("sessionUtils", () => {
	describe("generateSecureSessionId", () => {
		it("should generate a valid session ID", () => {
			const sessionId = generateSecureSessionId();
			expect(sessionId).toBeDefined();
			expect(typeof sessionId).toBe("string");
			expect(sessionId.length).toBeGreaterThan(0);
		});

		it("should generate unique session IDs", () => {
			const sessionId1 = generateSecureSessionId();
			const sessionId2 = generateSecureSessionId();
			expect(sessionId1).not.toBe(sessionId2);
		});

		it("should generate session IDs in valid format", () => {
			const sessionId = generateSecureSessionId();
			expect(isValidSessionIdFormat(sessionId)).toBe(true);
		});
	});

	describe("isValidSessionIdFormat", () => {
		it("should validate UUID v4 format", () => {
			const uuidV4 = "550e8400-e29b-41d4-a716-************";
			expect(isValidSessionIdFormat(uuidV4)).toBe(true);
		});

		it("should validate sess_ hex format", () => {
			const sessHex = "sess_" + "a".repeat(32);
			expect(isValidSessionIdFormat(sessHex)).toBe(true);
		});

		it("should validate sess_fallback_ format", () => {
			const sessFallback = "sess_fallback_abc123_def456789012";
			expect(isValidSessionIdFormat(sessFallback)).toBe(true);
		});

		it("should reject invalid formats", () => {
			expect(isValidSessionIdFormat("")).toBe(false);
			expect(isValidSessionIdFormat("invalid")).toBe(false);
			expect(isValidSessionIdFormat("sess_tooshort")).toBe(false);
			expect(isValidSessionIdFormat("not-a-uuid")).toBe(false);
		});

		it("should reject non-string inputs", () => {
			expect(isValidSessionIdFormat(null as any)).toBe(false);
			expect(isValidSessionIdFormat(undefined as any)).toBe(false);
			expect(isValidSessionIdFormat(123 as any)).toBe(false);
		});
	});
});
