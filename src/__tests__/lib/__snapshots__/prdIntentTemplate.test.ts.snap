// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`PRD Intent Template > Template Structure > should match template structure snapshot 1`] = `
{
  "clarificationMode": {
    "confidenceThreshold": 0.6,
    "keywords": {
      "primary": [
        "what",
        "how",
        "why",
        "when",
        "where",
        "who",
        "tell me",
        "explain",
        "describe",
        "clarify",
        "help me understand",
        "i need to know",
      ],
      "secondary": [
        "question",
        "confused",
        "not sure",
        "unclear",
        "more details",
        "can you",
        "could you",
      ],
    },
    "patterns": [
      {
        "confidence": 0.7,
        "pattern": /\\\\b\\(what\\|how\\|why\\|when\\|where\\|who\\)\\\\b/i,
        "rule": "question_word",
      },
      {
        "confidence": 0.8,
        "pattern": /\\\\b\\(tell me\\|explain\\|describe\\|clarify\\)\\\\b/i,
        "rule": "clarification_request",
      },
    ],
  },
  "generationMode": {
    "confidenceThreshold": 0.8,
    "contextBoosters": {
      "clarificationComplete": [
        "that's clear",
        "yes that's right",
        "exactly",
        "perfect",
        "that's correct",
        "sounds good",
        "that works",
        "i agree",
        "that's it",
        "that's the one",
        "makes sense",
        "understood",
        "clear",
        "got it",
        "that's right",
      ],
      "completionSignals": [
        "all set",
        "we're good",
        "that covers it",
        "nothing else",
        "that's complete",
        "ready to move on",
        "i'm done",
        "that's everything",
        "no more questions",
      ],
      "prdTerminology": [
        "requirements",
        "specification",
        "document",
        "feature",
        "user story",
        "acceptance criteria",
        "success metrics",
        "target user",
        "problem statement",
        "solution",
        "functionality",
        "business objective",
      ],
    },
    "keywords": {
      "primary": [
        "generate prd",
        "create prd",
        "write prd",
        "build prd",
        "make prd",
        "draft prd",
        "generate the prd",
        "create the prd",
        "write the prd",
        "build the prd",
        "make the prd",
        "draft the prd",
        "generate my prd",
        "create my prd",
        "write my prd",
        "build my prd",
        "make my prd",
        "let's generate",
        "ready to generate",
        "time to generate",
        "now generate",
        "please generate",
        "ready for prd",
        "ready for the prd",
        "i'm ready",
        "we're ready",
        "let's create",
        "let's build",
      ],
      "secondary": [
        "generate it",
        "create it",
        "write it up",
        "put it together",
        "make the document",
        "create the document",
        "write the document",
        "let's do this",
        "let's start",
        "i'm done",
        "that's everything",
        "sounds good",
        "perfect",
        "exactly",
        "that's clear",
        "yes that's right",
        "now what",
        "next step",
        "what's next",
        "ready to proceed",
        "let's move forward",
      ],
    },
    "patterns": [
      {
        "confidence": 0.95,
        "pattern": /\\\\b\\(generate\\|create\\|write\\|build\\|make\\|draft\\)\\\\s\\+\\(the\\\\s\\+\\|my\\\\s\\+\\)\\?prd\\\\b/i,
        "rule": "explicit_prd_request",
      },
      {
        "confidence": 0.9,
        "pattern": /\\\\bprd\\\\s\\+\\(generation\\|creation\\|writing\\|building\\)\\\\b/i,
        "rule": "prd_action_noun",
      },
      {
        "confidence": 0.8,
        "context": [
          "generate",
          "create",
          "prd",
        ],
        "pattern": /\\\\b\\(i'm\\|we're\\)\\\\s\\+ready\\\\s\\+\\(for\\|to\\)\\\\b/i,
        "rule": "readiness_indicator",
      },
      {
        "confidence": 0.75,
        "context": [
          "prd",
          "generation",
          "document",
        ],
        "pattern": /\\\\b\\(let's\\\\s\\+\\)\\?\\(get\\\\s\\+\\)\\?started\\\\s\\+\\(with\\|on\\)\\\\b/i,
        "rule": "start_action",
      },
      {
        "confidence": 0.8,
        "pattern": /\\\\bnow\\\\s\\+\\(generate\\|create\\|write\\|build\\)\\\\b/i,
        "rule": "immediate_action",
      },
      {
        "confidence": 0.7,
        "pattern": /\\\\b\\(that's\\\\s\\+\\)\\?\\(exactly\\|perfect\\|correct\\|right\\)\\\\b/i,
        "rule": "completion_confirmation",
      },
      {
        "confidence": 0.75,
        "pattern": /\\\\b\\(yes,\\?\\\\s\\+\\)\\?\\(that's\\\\s\\+it\\|that\\\\s\\+works\\)\\\\b/i,
        "rule": "agreement_completion",
      },
      {
        "confidence": 0.7,
        "pattern": /\\\\bsounds\\\\s\\+\\(good\\|great\\|perfect\\)\\\\b/i,
        "rule": "approval_signal",
      },
    ],
  },
}
`;
