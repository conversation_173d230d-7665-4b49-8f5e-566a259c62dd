/**
 * Comprehensive unit tests for PRD Intent Template & Keyword Library
 * Tests cover ≥50 positive/negative phrases, edge cases, confidence thresholds,
 * and template structure validation
 */

import { describe, it, expect } from "vitest";
import {
	matchIntent,
	matchIntentWithContext,
	prdIntentTemplate,
} from "../../lib/prdIntentTemplate";

describe("PRD Intent Template", () => {
	describe("Template Structure", () => {
		it("should have correct structure for clarificationMode", () => {
			expect(prdIntentTemplate.clarificationMode).toHaveProperty("keywords");
			expect(prdIntentTemplate.clarificationMode).toHaveProperty("patterns");
			expect(prdIntentTemplate.clarificationMode).toHaveProperty(
				"confidenceThreshold",
			);
			expect(prdIntentTemplate.clarificationMode.keywords).toHaveProperty(
				"primary",
			);
			expect(prdIntentTemplate.clarificationMode.keywords).toHaveProperty(
				"secondary",
			);
		});

		it("should have correct structure for generationMode", () => {
			expect(prdIntentTemplate.generationMode).toHaveProperty("keywords");
			expect(prdIntentTemplate.generationMode).toHaveProperty("patterns");
			expect(prdIntentTemplate.generationMode).toHaveProperty(
				"confidenceThreshold",
			);
			expect(prdIntentTemplate.generationMode).toHaveProperty(
				"contextBoosters",
			);
			expect(prdIntentTemplate.generationMode.contextBoosters).toHaveProperty(
				"clarificationComplete",
			);
			expect(prdIntentTemplate.generationMode.contextBoosters).toHaveProperty(
				"prdTerminology",
			);
			expect(prdIntentTemplate.generationMode.contextBoosters).toHaveProperty(
				"completionSignals",
			);
		});

		it("should have valid confidence thresholds", () => {
			expect(
				prdIntentTemplate.clarificationMode.confidenceThreshold,
			).toBeGreaterThan(0);
			expect(
				prdIntentTemplate.clarificationMode.confidenceThreshold,
			).toBeLessThanOrEqual(1);
			expect(
				prdIntentTemplate.generationMode.confidenceThreshold,
			).toBeGreaterThan(0);
			expect(
				prdIntentTemplate.generationMode.confidenceThreshold,
			).toBeLessThanOrEqual(1);
		});

		it("should match template structure snapshot", () => {
			expect(prdIntentTemplate).toMatchSnapshot();
		});
	});

	describe("matchIntent - Positive PRD Generation Cases", () => {
		const positiveTestCases = [
			// Explicit PRD requests (high confidence)
			{
				input: "generate prd",
				expectedIntent: "generation",
				minConfidence: 0.9,
			},
			{ input: "create prd", expectedIntent: "generation", minConfidence: 0.9 },
			{ input: "write prd", expectedIntent: "generation", minConfidence: 0.9 },
			{ input: "build prd", expectedIntent: "generation", minConfidence: 0.9 },
			{ input: "make prd", expectedIntent: "generation", minConfidence: 0.9 },
			{ input: "draft prd", expectedIntent: "generation", minConfidence: 0.9 },

			// With articles
			{
				input: "generate the prd",
				expectedIntent: "generation",
				minConfidence: 0.9,
			},
			{
				input: "create the prd",
				expectedIntent: "generation",
				minConfidence: 0.9,
			},
			{
				input: "write the prd",
				expectedIntent: "generation",
				minConfidence: 0.9,
			},

			// Possessive forms
			{
				input: "generate my prd",
				expectedIntent: "generation",
				minConfidence: 0.9,
			},
			{
				input: "create my prd",
				expectedIntent: "generation",
				minConfidence: 0.9,
			},
			{
				input: "write my prd",
				expectedIntent: "generation",
				minConfidence: 0.9,
			},

			// Action-oriented
			{
				input: "let's generate",
				expectedIntent: "generation",
				minConfidence: 0.7,
			},
			{
				input: "ready to generate",
				expectedIntent: "generation",
				minConfidence: 0.7,
			},
			{
				input: "time to generate",
				expectedIntent: "generation",
				minConfidence: 0.7,
			},
			{
				input: "now generate",
				expectedIntent: "generation",
				minConfidence: 0.8,
			},
			{
				input: "please generate",
				expectedIntent: "generation",
				minConfidence: 0.7,
			},

			// Completion indicators
			{
				input: "ready for prd",
				expectedIntent: "generation",
				minConfidence: 0.7,
			},
			{
				input: "ready for the prd",
				expectedIntent: "generation",
				minConfidence: 0.7,
			},
			{ input: "i'm ready", expectedIntent: "generation", minConfidence: 0.7 },
			{
				input: "we're ready",
				expectedIntent: "generation",
				minConfidence: 0.7,
			},
			{
				input: "let's create",
				expectedIntent: "generation",
				minConfidence: 0.7,
			},
			{
				input: "let's build",
				expectedIntent: "generation",
				minConfidence: 0.7,
			},

			// Natural language patterns
			{
				input: "generate it",
				expectedIntent: "generation",
				minConfidence: 0.6,
			},
			{ input: "create it", expectedIntent: "generation", minConfidence: 0.6 },
			{
				input: "write it up",
				expectedIntent: "generation",
				minConfidence: 0.6,
			},
			{
				input: "put it together",
				expectedIntent: "generation",
				minConfidence: 0.6,
			},

			// Case insensitive
			{
				input: "GENERATE PRD",
				expectedIntent: "generation",
				minConfidence: 0.9,
			},
			{ input: "Create PRD", expectedIntent: "generation", minConfidence: 0.9 },
			{
				input: "Write The PRD",
				expectedIntent: "generation",
				minConfidence: 0.9,
			},

			// With punctuation
			{
				input: "generate prd!",
				expectedIntent: "generation",
				minConfidence: 0.9,
			},
			{
				input: "create prd.",
				expectedIntent: "generation",
				minConfidence: 0.9,
			},
			{ input: "write prd?", expectedIntent: "generation", minConfidence: 0.9 },

			// In sentences
			{
				input: "I think we should generate prd now",
				expectedIntent: "generation",
				minConfidence: 0.9,
			},
			{
				input: "Can you create prd for me?",
				expectedIntent: "generation",
				minConfidence: 0.9,
			},
			{
				input: "Please write the prd document",
				expectedIntent: "generation",
				minConfidence: 0.9,
			},
		];

		positiveTestCases.forEach(({ input, expectedIntent, minConfidence }) => {
			it(`should detect "${input}" as ${expectedIntent} intent with confidence >= ${minConfidence}`, () => {
				const result = matchIntent(input);
				expect(result.intent).toBe(expectedIntent);
				expect(result.confidence).toBeGreaterThanOrEqual(minConfidence);
				expect(result.rule).toBeDefined();
				expect(typeof result.rule).toBe("string");
			});
		});
	});

	describe("matchIntent - Negative Cases (Should NOT trigger PRD generation)", () => {
		const negativeTestCases = [
			// Questions about PRDs
			{ input: "what is a prd", expectedIntent: "clarification" },
			{ input: "how to write a prd", expectedIntent: "clarification" },
			{ input: "prd template", expectedIntent: "clarification" },
			{ input: "prd example", expectedIntent: "clarification" },
			{ input: "show me a prd", expectedIntent: "clarification" },

			// Hypothetical scenarios
			{ input: "if we generate prd", expectedIntent: "clarification" },
			{ input: "should we create prd", expectedIntent: "clarification" },
			{ input: "maybe we could generate", expectedIntent: "clarification" },
			{ input: "what if we create prd", expectedIntent: "clarification" },
			{ input: "perhaps we should write", expectedIntent: "clarification" },

			// Past tense references
			{ input: "generated a prd", expectedIntent: "clarification" },
			{ input: "created a prd", expectedIntent: "clarification" },
			{ input: "wrote a prd", expectedIntent: "clarification" },
			{ input: "we generated prd yesterday", expectedIntent: "clarification" },

			// Conditional statements
			{ input: "before we generate prd", expectedIntent: "clarification" },
			{ input: "after we create prd", expectedIntent: "clarification" },
			{ input: "once we have prd", expectedIntent: "clarification" },

			// General conversation
			{ input: "hello", expectedIntent: "clarification" },
			{ input: "how are you", expectedIntent: "clarification" },
			{ input: "tell me about your product", expectedIntent: "clarification" },
			{ input: "what can you do", expectedIntent: "clarification" },

			// Partial matches that should not trigger
			{ input: "regenerate", expectedIntent: "clarification" },
			{ input: "procreate", expectedIntent: "clarification" },
			{ input: "generate power", expectedIntent: "clarification" },
			{ input: "create account", expectedIntent: "clarification" },
		];

		negativeTestCases.forEach(({ input, expectedIntent }) => {
			it(`should NOT detect "${input}" as generation intent`, () => {
				const result = matchIntent(input);
				expect(result.intent).toBe(expectedIntent);
			});
		});
	});

	describe("matchIntent - Edge Cases", () => {
		it("should handle empty string", () => {
			const result = matchIntent("");
			expect(result.intent).toBe("clarification");
			expect(result.confidence).toBeLessThan(0.5);
		});

		it("should handle whitespace only", () => {
			const result = matchIntent("   ");
			expect(result.intent).toBe("clarification");
			expect(result.confidence).toBeLessThan(0.5);
		});

		it("should handle very long text", () => {
			const longText = "a".repeat(1000) + " generate prd " + "b".repeat(1000);
			const result = matchIntent(longText);
			expect(result.intent).toBe("generation");
			expect(result.confidence).toBeGreaterThan(0.8);
		});

		it("should handle special characters", () => {
			const result = matchIntent("!@#$%^&*()generate prd{}[]|\\");
			expect(result.intent).toBe("generation");
			expect(result.confidence).toBeGreaterThan(0.8);
		});

		it("should handle unicode characters", () => {
			const result = matchIntent("génerate prd 🚀");
			expect(result.intent).toBe("clarification"); // Should not match due to accent
		});
	});

	describe("matchIntentWithContext", () => {
		it("should boost confidence with clarification completion indicators", () => {
			const baseResult = matchIntent("let's do this");
			const contextResult = matchIntentWithContext(
				"let's do this",
				["that's clear", "exactly", "perfect"],
				10,
			);

			expect(contextResult.confidence).toBeGreaterThan(baseResult.confidence);
		});

		it("should boost confidence with PRD terminology", () => {
			const contextResult = matchIntentWithContext(
				"sounds good",
				["requirements", "user story", "success metrics", "target user"],
				8,
			);

			expect(contextResult.confidence).toBeGreaterThan(0.7);
		});

		it("should boost confidence with conversation length", () => {
			const shortResult = matchIntentWithContext("ready", [], 3);
			const longResult = matchIntentWithContext("ready", [], 12);

			expect(longResult.confidence).toBeGreaterThanOrEqual(
				shortResult.confidence,
			);
		});

		it("should not change high confidence results", () => {
			const highConfidenceInput = "generate prd";
			const baseResult = matchIntent(highConfidenceInput);
			const contextResult = matchIntentWithContext(highConfidenceInput, [], 5);

			expect(contextResult.confidence).toBe(baseResult.confidence);
			expect(contextResult.intent).toBe(baseResult.intent);
		});
	});

	describe("Confidence Thresholds", () => {
		it("should respect generation mode confidence threshold", () => {
			const threshold = prdIntentTemplate.generationMode.confidenceThreshold;

			// Test a borderline case
			const result = matchIntent("maybe generate");
			if (result.confidence >= threshold) {
				expect(result.intent).toBe("generation");
			} else {
				expect(result.intent).toBe("clarification");
			}
		});

		it("should have consistent confidence scoring", () => {
			const result1 = matchIntent("generate prd");
			const result2 = matchIntent("generate prd");

			expect(result1.confidence).toBe(result2.confidence);
			expect(result1.intent).toBe(result2.intent);
			expect(result1.rule).toBe(result2.rule);
		});
	});

	describe("Rule Tracking", () => {
		it("should provide rule names for debugging", () => {
			const result = matchIntent("generate prd");
			expect(result.rule).toBeDefined();
			expect(typeof result.rule).toBe("string");
			expect(result.rule.length).toBeGreaterThan(0);
		});

		it("should provide matched pattern for analysis", () => {
			const result = matchIntent("create the prd");
			expect(result.matchedPattern).toBeDefined();
		});

		it("should track negative rule applications", () => {
			const result = matchIntent("what is a prd");
			expect(result.rule).toContain("negative_");
		});
	});
});
