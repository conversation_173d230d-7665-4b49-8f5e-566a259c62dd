import { render, screen, waitFor } from "../utils/test-utils";
import MessageList from "../../components/MessageList";
import {
	mockMessages,
	mockEmptyMessages,
	mockLongMessage,
} from "../utils/mock-data";
import { Message } from "../../types";
import { vi, describe, beforeEach, it, expect } from "vitest";

// Mock the Message component to focus on MessageList logic
vi.mock("../../components/Message", () => {
	return {
		default: function MockMessage({ message }: { message: Message }) {
			return (
				<div data-testid={`message-${message.id}`} data-role={message.role}>
					{message.content}
				</div>
			);
		},
	};
});

describe("MessageList", () => {
	beforeEach(() => {
		vi.clearAllMocks();
		// Mock scrollIntoView
		Element.prototype.scrollIntoView = vi.fn();
	});

	describe("Rendering", () => {
		it("renders all messages correctly", () => {
			render(<MessageList messages={mockMessages} isLoading={false} />);

			expect(screen.getByTestId("message-1")).toBeInTheDocument();
			expect(screen.getByTestId("message-2")).toBeInTheDocument();
			expect(screen.getByTestId("message-3")).toBeInTheDocument();
		});

		it("renders messages in correct order", () => {
			render(<MessageList messages={mockMessages} isLoading={false} />);

			const messages = screen.getAllByTestId(/message-/);
			expect(messages).toHaveLength(3);
			expect(messages[0]).toHaveAttribute("data-testid", "message-1");
			expect(messages[1]).toHaveAttribute("data-testid", "message-2");
			expect(messages[2]).toHaveAttribute("data-testid", "message-3");
		});

		it("displays empty state when no messages", () => {
			render(<MessageList messages={mockEmptyMessages} isLoading={false} />);

			expect(screen.getByText(/welcome to prdgeneral/i)).toBeInTheDocument();
			expect(
				screen.getByText(/tell me about your product idea/i),
			).toBeInTheDocument();
		});

		it("has proper accessibility attributes", () => {
			render(<MessageList messages={mockMessages} isLoading={false} />);

			const messageList = screen.getByRole("log");
			expect(messageList).toHaveAttribute("aria-label", "Chat messages");
			expect(messageList).toHaveAttribute("aria-live", "polite");
		});
	});

	describe("Loading States", () => {
		it("shows loading indicator when isLoading is true", () => {
			render(<MessageList messages={mockMessages} isLoading={true} />);

			expect(screen.getByText(/thinking/i)).toBeInTheDocument();
			expect(screen.getByRole("status")).toBeInTheDocument();
		});

		it("shows loading animation", () => {
			const { container } = render(
				<MessageList messages={mockMessages} isLoading={true} />,
			);

			const loadingDots = container.querySelectorAll(".animate-bounce");
			expect(loadingDots.length).toBeGreaterThan(0);
		});

		it("does not show loading indicator when not loading", () => {
			render(<MessageList messages={mockMessages} isLoading={false} />);

			expect(screen.queryByText(/thinking/i)).not.toBeInTheDocument();
			expect(screen.queryByRole("status")).not.toBeInTheDocument();
		});
	});

	describe("Auto-scroll Behavior", () => {
		it("scrolls to bottom when new messages are added", async () => {
			const { rerender } = render(
				<MessageList messages={mockMessages} isLoading={false} />,
			);

			const newMessage = {
				id: "4",
				role: "user" as const,
				content: "New message",
			};

			rerender(
				<MessageList
					messages={[...mockMessages, newMessage]}
					isLoading={false}
				/>,
			);

			await waitFor(() => {
				expect(Element.prototype.scrollIntoView).toHaveBeenCalled();
			});
		});

		it("scrolls to bottom when loading state changes", async () => {
			const { rerender } = render(
				<MessageList messages={mockMessages} isLoading={false} />,
			);

			rerender(<MessageList messages={mockMessages} isLoading={true} />);

			await waitFor(() => {
				expect(Element.prototype.scrollIntoView).toHaveBeenCalled();
			});
		});

		it("debounces scroll calls", async () => {
			vi.useFakeTimers();

			const { rerender } = render(
				<MessageList messages={mockMessages} isLoading={false} />,
			);

			// Clear any initial calls
			vi.clearAllMocks();

			// Trigger multiple rapid updates
			rerender(
				<MessageList
					messages={[
						...mockMessages,
						{ id: "4", role: "user", content: "Test 1", timestamp: Date.now() },
					]}
					isLoading={false}
				/>,
			);
			rerender(
				<MessageList
					messages={[
						...mockMessages,
						{ id: "5", role: "user", content: "Test 2", timestamp: Date.now() },
					]}
					isLoading={false}
				/>,
			);
			rerender(
				<MessageList
					messages={[
						...mockMessages,
						{ id: "6", role: "user", content: "Test 3", timestamp: Date.now() },
					]}
					isLoading={false}
				/>,
			);

			// Fast-forward time to trigger debounced function
			await vi.advanceTimersByTimeAsync(100);

			// Should only be called once due to debouncing
			expect(Element.prototype.scrollIntoView).toHaveBeenCalledTimes(1);

			vi.useRealTimers();
		});
	});

	describe("Message Content Handling", () => {
		it("handles long messages correctly", () => {
			render(<MessageList messages={[mockLongMessage]} isLoading={false} />);

			expect(screen.getByTestId("message-4")).toBeInTheDocument();
			expect(screen.getByTestId("message-4")).toHaveTextContent(
				"A".repeat(5000),
			);
		});

		it("handles messages with different roles", () => {
			render(<MessageList messages={mockMessages} isLoading={false} />);

			expect(screen.getByTestId("message-1")).toHaveAttribute(
				"data-role",
				"user",
			);
			expect(screen.getByTestId("message-2")).toHaveAttribute(
				"data-role",
				"assistant",
			);
			expect(screen.getByTestId("message-3")).toHaveAttribute(
				"data-role",
				"user",
			);
		});
	});

	describe("Responsive Design", () => {
		it("applies correct CSS classes for responsive layout", () => {
			render(<MessageList messages={mockMessages} isLoading={false} />);

			const container = screen.getByRole("log");
			expect(container).toHaveClass("flex-1", "overflow-y-auto");
		});

		it("handles empty state responsively", () => {
			render(<MessageList messages={mockEmptyMessages} isLoading={false} />);

			const emptyState = screen
				.getByText(/welcome to prdgeneral/i)
				.closest("div");
			expect(emptyState).toHaveClass("text-center", "text-gray-500");
		});
	});

	describe("Performance", () => {
		it("renders efficiently with many messages", () => {
			const manyMessages = Array.from({ length: 100 }, (_, i) => ({
				id: `msg-${i}`,
				role: i % 2 === 0 ? ("user" as const) : ("assistant" as const),
				content: `Message ${i}`,
			}));

			const startTime = performance.now();
			render(<MessageList messages={manyMessages} isLoading={false} />);
			const endTime = performance.now();

			// Should render within reasonable time (less than 100ms)
			expect(endTime - startTime).toBeLessThan(100);
			expect(screen.getAllByTestId(/message-/)).toHaveLength(100);
		});
	});

	describe("Error Handling", () => {
		it("handles undefined messages gracefully", () => {
			render(
				<MessageList
					messages={undefined as unknown as Message[]}
					isLoading={false}
				/>,
			);

			expect(screen.getByText(/welcome to prdgeneral/i)).toBeInTheDocument();
		});

		it("handles null messages gracefully", () => {
			render(
				<MessageList
					messages={null as unknown as Message[]}
					isLoading={false}
				/>,
			);

			expect(screen.getByText(/welcome to prdgeneral/i)).toBeInTheDocument();
		});

		it("handles messages with missing properties", () => {
			const invalidMessages = [
				{ id: "1", role: "user" }, // missing content
				{ id: "2", content: "Test" }, // missing role
			] as unknown as Message[];

			render(<MessageList messages={invalidMessages} isLoading={false} />);

			// Should not crash and render what it can
			expect(screen.getByTestId("message-1")).toBeInTheDocument();
			expect(screen.getByTestId("message-2")).toBeInTheDocument();
		});
	});
});
