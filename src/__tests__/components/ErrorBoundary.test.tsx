import { render, screen } from "../utils/test-utils";
import ErrorBoundary from "../../components/ErrorBoundary";
import React from "react";
import {
	vi,
	describe,
	beforeAll,
	afterAll,
	beforeEach,
	it,
	expect,
} from "vitest";

// Component that throws an error for testing
const ThrowError = ({ shouldThrow }: { shouldThrow: boolean }) => {
	if (shouldThrow) {
		throw new Error("Test error");
	}
	return <div>No error</div>;
};

// Component that throws an error during render
const RenderError = () => {
	throw new Error("Render error");
};

// Mock console.error to avoid noise in test output
const originalError = console.error;
beforeAll(() => {
	console.error = vi.fn();
});

afterAll(() => {
	console.error = originalError;
});

describe("ErrorBoundary", () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	describe("Normal Operation", () => {
		it("renders children when there is no error", () => {
			render(
				<ErrorBoundary>
					<div>Test content</div>
				</ErrorBoundary>,
			);

			expect(screen.getByText("Test content")).toBeInTheDocument();
		});

		it("renders multiple children correctly", () => {
			render(
				<ErrorBoundary>
					<div>Child 1</div>
					<div>Child 2</div>
				</ErrorBoundary>,
			);

			expect(screen.getByText("Child 1")).toBeInTheDocument();
			expect(screen.getByText("Child 2")).toBeInTheDocument();
		});
	});

	describe("Error Handling", () => {
		it("catches and displays error fallback UI", () => {
			render(
				<ErrorBoundary>
					<RenderError />
				</ErrorBoundary>,
			);

			expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
			expect(
				screen.getByText(/we apologize for the inconvenience/i),
			).toBeInTheDocument();
		});

		it("renders custom fallback when provided", () => {
			const customFallback = (
				<div data-testid="custom-fallback">Custom error message</div>
			);

			render(
				<ErrorBoundary fallback={customFallback}>
					<RenderError />
				</ErrorBoundary>,
			);

			expect(screen.getByTestId("custom-fallback")).toBeInTheDocument();
			expect(screen.getByText("Custom error message")).toBeInTheDocument();
			expect(
				screen.queryByText(/something went wrong/i),
			).not.toBeInTheDocument();
		});

		it("renders custom fallback with complex JSX", () => {
			const customFallback = (
				<div data-testid="complex-fallback">
					<h2>Custom Error Title</h2>
					<p>Custom error description</p>
					<button>Custom Action</button>
				</div>
			);

			render(
				<ErrorBoundary fallback={customFallback}>
					<RenderError />
				</ErrorBoundary>,
			);

			expect(screen.getByTestId("complex-fallback")).toBeInTheDocument();
			expect(screen.getByText("Custom Error Title")).toBeInTheDocument();
			expect(screen.getByText("Custom error description")).toBeInTheDocument();
			expect(
				screen.getByRole("button", { name: "Custom Action" }),
			).toBeInTheDocument();
		});

		it("renders custom fallback as string", () => {
			const customFallback = "Simple error message";

			render(
				<ErrorBoundary fallback={customFallback}>
					<RenderError />
				</ErrorBoundary>,
			);

			expect(screen.getByText("Simple error message")).toBeInTheDocument();
			expect(
				screen.queryByText(/something went wrong/i),
			).not.toBeInTheDocument();
		});

		it("renders default error UI when fallback is null", () => {
			render(
				<ErrorBoundary fallback={null}>
					<RenderError />
				</ErrorBoundary>,
			);

			// When fallback is null (falsy), should render default error UI
			expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
			expect(screen.getByRole("alert")).toBeInTheDocument();
		});

		it("shows try again button in error state", () => {
			render(
				<ErrorBoundary>
					<RenderError />
				</ErrorBoundary>,
			);

			expect(
				screen.getByRole("button", { name: /try again/i }),
			).toBeInTheDocument();
		});

		it("displays error details in development mode", () => {
			vi.stubEnv("NODE_ENV", "development");

			render(
				<ErrorBoundary>
					<RenderError />
				</ErrorBoundary>,
			);

			expect(screen.getByText(/error details/i)).toBeInTheDocument();
			expect(screen.getByText(/render error/i)).toBeInTheDocument();

			vi.unstubAllEnvs();
		});

		it("hides error details in production mode", () => {
			vi.stubEnv("NODE_ENV", "production");

			render(
				<ErrorBoundary>
					<RenderError />
				</ErrorBoundary>,
			);

			expect(screen.queryByText(/error details/i)).not.toBeInTheDocument();
			expect(screen.queryByText(/render error/i)).not.toBeInTheDocument();

			vi.unstubAllEnvs();
		});
	});

	describe("Error Recovery", () => {
		it("recovers from error when try again is clicked", () => {
			const { rerender } = render(
				<ErrorBoundary>
					<ThrowError shouldThrow={true} />
				</ErrorBoundary>,
			);

			// Should show error UI
			expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();

			// Click try again
			const tryAgainButton = screen.getByRole("button", { name: /try again/i });
			tryAgainButton.click();

			// Re-render with non-throwing component
			rerender(
				<ErrorBoundary>
					<ThrowError shouldThrow={false} />
				</ErrorBoundary>,
			);

			// Should show normal content
			expect(screen.getByText("No error")).toBeInTheDocument();
			expect(
				screen.queryByText(/something went wrong/i),
			).not.toBeInTheDocument();
		});

		it("resets error state when children change", () => {
			const { rerender } = render(
				<ErrorBoundary>
					<RenderError />
				</ErrorBoundary>,
			);

			// Should show error UI
			expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();

			// Click try again to reset error state
			const tryAgainButton = screen.getByRole("button", { name: /try again/i });
			tryAgainButton.click();

			// Re-render with different children
			rerender(
				<ErrorBoundary>
					<div>New content</div>
				</ErrorBoundary>,
			);

			// Should show new content
			expect(screen.getByText("New content")).toBeInTheDocument();
			expect(
				screen.queryByText(/something went wrong/i),
			).not.toBeInTheDocument();
		});
	});

	describe("Error Logging", () => {
		it("logs error to console", () => {
			const consoleSpy = vi
				.spyOn(console, "error")
				.mockImplementation(() => {});

			render(
				<ErrorBoundary>
					<RenderError />
				</ErrorBoundary>,
			);

			expect(consoleSpy).toHaveBeenCalledWith(
				"ErrorBoundary caught an error:",
				expect.any(Error),
				expect.any(Object),
			);

			consoleSpy.mockRestore();
		});

		it("includes error info in log", () => {
			const consoleSpy = vi
				.spyOn(console, "error")
				.mockImplementation(() => {});

			render(
				<ErrorBoundary>
					<RenderError />
				</ErrorBoundary>,
			);

			expect(consoleSpy).toHaveBeenCalledWith(
				"ErrorBoundary caught an error:",
				expect.objectContaining({
					message: "Render error",
				}),
				expect.objectContaining({
					componentStack: expect.any(String),
				}),
			);

			consoleSpy.mockRestore();
		});
	});

	describe("Accessibility", () => {
		it("has proper ARIA attributes in error state", () => {
			render(
				<ErrorBoundary>
					<RenderError />
				</ErrorBoundary>,
			);

			const errorContainer = screen.getByRole("alert");
			expect(errorContainer).toBeInTheDocument();
			expect(errorContainer).toHaveAttribute("aria-live", "assertive");
		});

		it("has accessible button in error state", () => {
			render(
				<ErrorBoundary>
					<RenderError />
				</ErrorBoundary>,
			);

			const tryAgainButton = screen.getByRole("button", { name: /try again/i });
			expect(tryAgainButton).toHaveAttribute("type", "button");
		});

		it("provides screen reader friendly error message", () => {
			render(
				<ErrorBoundary>
					<RenderError />
				</ErrorBoundary>,
			);

			expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
			expect(
				screen.getByText(/we apologize for the inconvenience/i),
			).toBeInTheDocument();
		});
	});

	describe("Styling and Layout", () => {
		it("applies correct CSS classes to error container", () => {
			render(
				<ErrorBoundary>
					<RenderError />
				</ErrorBoundary>,
			);

			const errorContainer = screen.getByRole("alert");
			expect(errorContainer).toHaveClass("bg-red-50", "dark:bg-red-900/20");
		});

		it("styles try again button correctly", () => {
			render(
				<ErrorBoundary>
					<RenderError />
				</ErrorBoundary>,
			);

			const tryAgainButton = screen.getByRole("button", { name: /try again/i });
			expect(tryAgainButton).toHaveClass(
				"bg-red-600",
				"text-white",
				"px-4",
				"py-2",
				"rounded-lg",
			);
		});

		it("includes dark mode support", () => {
			render(
				<ErrorBoundary>
					<RenderError />
				</ErrorBoundary>,
			);

			const errorContainer = screen.getByRole("alert");
			expect(errorContainer).toHaveClass("dark:bg-red-900/20");
		});
	});

	describe("Edge Cases", () => {
		it("handles null children gracefully", () => {
			render(<ErrorBoundary>{null}</ErrorBoundary>);

			// Should not crash
			expect(
				screen.queryByText(/something went wrong/i),
			).not.toBeInTheDocument();
		});

		it("handles undefined children gracefully", () => {
			render(<ErrorBoundary>{undefined}</ErrorBoundary>);

			// Should not crash
			expect(
				screen.queryByText(/something went wrong/i),
			).not.toBeInTheDocument();
		});

		it("handles errors in error boundary itself", () => {
			// This is a complex scenario - the error boundary should be robust
			render(
				<ErrorBoundary>
					<RenderError />
				</ErrorBoundary>,
			);

			// Should still show error UI even if there are issues with the boundary
			expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
		});
	});

	describe("Component Lifecycle", () => {
		it("resets state when component unmounts and remounts", () => {
			const { unmount } = render(
				<ErrorBoundary>
					<RenderError />
				</ErrorBoundary>,
			);

			// Should show error
			expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();

			unmount();

			// Render a fresh component
			render(
				<ErrorBoundary>
					<div>Fresh start</div>
				</ErrorBoundary>,
			);

			// Should show normal content
			expect(screen.getByText("Fresh start")).toBeInTheDocument();
			expect(
				screen.queryByText(/something went wrong/i),
			).not.toBeInTheDocument();
		});
	});
});
