name: Test Suite

on:
  # push:
  #   branches: [ main, develop, 4-build-core-chat-interface, fix-ci-tests ]
  pull_request:
    branches: [ main, develop ]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: read
  issues: read
  pull-requests: write

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install devbox
      uses: jetify-com/devbox-install-action@v0.11.0
      with:
        enable-cache: true

    - name: Install dependencies
      run: devbox run -- pnpm install --frozen-lockfile

    - name: Run linting
      run: devbox run -- pnpm lint

    - name: Run unit tests with coverage
      run: devbox run -- pnpm test:coverage
      env:
        CI: true

    - name: Upload coverage artifacts
      uses: actions/upload-artifact@v4
      with:
        name: coverage-reports
        path: |
          coverage/
        retention-days: 1

    - name: Upload coverage reports to Codecov
      uses: codecov/codecov-action@v4
      with:
        file: ./coverage/lcov.info
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

    - name: Build application
      run: devbox run -- pnpm build
      env:
        ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
        NEXT_PUBLIC_CONVEX_URL: ${{ secrets.NEXT_PUBLIC_CONVEX_URL_DEV }}

  coverage-check:
    runs-on: ubuntu-latest
    needs: test

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download coverage artifacts
      uses: actions/download-artifact@v4
      with:
        name: coverage-reports
        path: coverage/

    - name: Coverage Summary
      run: |
        echo "## Test Coverage Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
        cat coverage/coverage-summary.json | jq -r '
          .total |
          "Lines: " + (.lines.pct | tostring) + "% (" + (.lines.covered | tostring) + "/" + (.lines.total | tostring) + ")",
          "Functions: " + (.functions.pct | tostring) + "% (" + (.functions.covered | tostring) + "/" + (.functions.total | tostring) + ")",
          "Branches: " + (.branches.pct | tostring) + "% (" + (.branches.covered | tostring) + "/" + (.branches.total | tostring) + ")",
          "Statements: " + (.statements.pct | tostring) + "% (" + (.statements.covered | tostring) + "/" + (.statements.total | tostring) + ")"
        ' >> $GITHUB_STEP_SUMMARY
        echo "\`\`\`" >> $GITHUB_STEP_SUMMARY

    # - name: Comment coverage on PR
    #   if: github.event_name == 'pull_request'
    #   uses: actions/github-script@v7
    #   with:
    #     script: |
    #       const fs = require('fs');
    #       const coverage = JSON.parse(fs.readFileSync('coverage/coverage-summary.json', 'utf8'));
    #       const total = coverage.total;

    #       const comment = `## 📊 Test Coverage Report

    #       | Metric | Coverage | Threshold |
    #       |--------|----------|-----------|
    #       | Lines | ${total.lines.pct}% | 85% |
    #       | Functions | ${total.functions.pct}% | 85% |
    #       | Branches | ${total.branches.pct}% | 85% |
    #       | Statements | ${total.statements.pct}% | 85% |

    #       ${total.lines.pct >= 85 && total.functions.pct >= 85 && total.branches.pct >= 85 && total.statements.pct >= 85
    #         ? '✅ All coverage thresholds met!'
    #         : '❌ Some coverage thresholds not met. Please add more tests.'}
    #       `;

    #       github.rest.issues.createComment({
    #         issue_number: context.issue.number,
    #         owner: context.repo.owner,
    #         repo: context.repo.repo,
    #         body: comment
    #       });
